// Generated by third_party/blink/renderer/build/scripts/gperf.py
/* C++ code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf --key-positions='*' -P -n -m 50 -D  */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif


// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "third_party/blink/renderer/core/css/parser/at_rule_descriptors.h"

#include "third_party/blink/renderer/core/css/hash_tools.h"

#ifdef _MSC_VER
// Disable the warnings from casting a 64-bit pointer to 32-bit long
// warning C4302: 'type cast': truncation from 'char (*)[28]' to 'long'
// warning C4311: 'type cast': pointer truncation from 'char (*)[18]' to 'long'
#pragma warning(disable : 4302 4311)
#endif

namespace blink {

namespace {

/* maximum key range = 48, duplicates = 0 */

class AtRuleDescriptorHash
{
private:
  static inline unsigned int descriptor_hash_function (const char *str, unsigned int len);
public:
  static const struct Property *findDescriptorImpl (const char *str, unsigned int len);
};

inline unsigned int
AtRuleDescriptorHash::descriptor_hash_function (const char *str, unsigned int len)
{
  static const unsigned char asso_values[] =
    {
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48,  1, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48,  0,  8,  0,
       0,  1,  7,  1, 17,  0,  2,  6,  4,  7,
       0,  0,  6, 48,  0,  0,  0,  7,  0,  3,
      10,  2,  2, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48, 48, 48, 48, 48,
      48, 48, 48, 48, 48, 48
    };
  unsigned int hval = 0;

  switch (len)
    {
      default:
        hval += asso_values[(unsigned char)str[28]];
        [[fallthrough]];
      case 28:
        hval += asso_values[(unsigned char)str[27]];
        [[fallthrough]];
      case 27:
        hval += asso_values[(unsigned char)str[26]];
        [[fallthrough]];
      case 26:
        hval += asso_values[(unsigned char)str[25]];
        [[fallthrough]];
      case 25:
        hval += asso_values[(unsigned char)str[24]];
        [[fallthrough]];
      case 24:
        hval += asso_values[(unsigned char)str[23]];
        [[fallthrough]];
      case 23:
        hval += asso_values[(unsigned char)str[22]];
        [[fallthrough]];
      case 22:
        hval += asso_values[(unsigned char)str[21]];
        [[fallthrough]];
      case 21:
        hval += asso_values[(unsigned char)str[20]];
        [[fallthrough]];
      case 20:
        hval += asso_values[(unsigned char)str[19]];
        [[fallthrough]];
      case 19:
        hval += asso_values[(unsigned char)str[18]];
        [[fallthrough]];
      case 18:
        hval += asso_values[(unsigned char)str[17]];
        [[fallthrough]];
      case 17:
        hval += asso_values[(unsigned char)str[16]];
        [[fallthrough]];
      case 16:
        hval += asso_values[(unsigned char)str[15]];
        [[fallthrough]];
      case 15:
        hval += asso_values[(unsigned char)str[14]];
        [[fallthrough]];
      case 14:
        hval += asso_values[(unsigned char)str[13]];
        [[fallthrough]];
      case 13:
        hval += asso_values[(unsigned char)str[12]];
        [[fallthrough]];
      case 12:
        hval += asso_values[(unsigned char)str[11]];
        [[fallthrough]];
      case 11:
        hval += asso_values[(unsigned char)str[10]];
        [[fallthrough]];
      case 10:
        hval += asso_values[(unsigned char)str[9]];
        [[fallthrough]];
      case 9:
        hval += asso_values[(unsigned char)str[8]];
        [[fallthrough]];
      case 8:
        hval += asso_values[(unsigned char)str[7]];
        [[fallthrough]];
      case 7:
        hval += asso_values[(unsigned char)str[6]];
        [[fallthrough]];
      case 6:
        hval += asso_values[(unsigned char)str[5]];
        [[fallthrough]];
      case 5:
        hval += asso_values[(unsigned char)str[4]];
        [[fallthrough]];
      case 4:
        hval += asso_values[(unsigned char)str[3]];
        [[fallthrough]];
      case 3:
        hval += asso_values[(unsigned char)str[2]];
        [[fallthrough]];
      case 2:
        hval += asso_values[(unsigned char)str[1]];
        [[fallthrough]];
      case 1:
        hval += asso_values[(unsigned char)str[0]];
        break;
    }
  return hval;
}

struct stringpool_t
  {
    char stringpool_str0[sizeof("src")];
    char stringpool_str1[sizeof("navigation")];
    char stringpool_str2[sizeof("range")];
    char stringpool_str3[sizeof("negative")];
    char stringpool_str4[sizeof("ascent-override")];
    char stringpool_str5[sizeof("descent-override")];
    char stringpool_str6[sizeof("pad")];
    char stringpool_str7[sizeof("override-colors")];
    char stringpool_str8[sizeof("font-variant")];
    char stringpool_str9[sizeof("types")];
    char stringpool_str10[sizeof("system")];
    char stringpool_str11[sizeof("unicode-range")];
    char stringpool_str12[sizeof("syntax")];
    char stringpool_str13[sizeof("size-adjust")];
    char stringpool_str14[sizeof("speak-as")];
    char stringpool_str15[sizeof("font-style")];
    char stringpool_str16[sizeof("line-gap-override")];
    char stringpool_str17[sizeof("initial-value")];
    char stringpool_str18[sizeof("inherits")];
    char stringpool_str19[sizeof("font-display")];
    char stringpool_str20[sizeof("symbols")];
    char stringpool_str21[sizeof("base-palette")];
    char stringpool_str22[sizeof("additive-symbols")];
    char stringpool_str23[sizeof("prefix")];
    char stringpool_str24[sizeof("font-stretch")];
    char stringpool_str25[sizeof("font-feature-settings")];
    char stringpool_str26[sizeof("font-family")];
    char stringpool_str27[sizeof("fallback")];
    char stringpool_str28[sizeof("font-weight")];
    char stringpool_str29[sizeof("suffix")];
    char stringpool_str30[sizeof("-webkit-font-feature-settings")];
  };
static const struct stringpool_t stringpool_contents =
  {
    "src",
    "navigation",
    "range",
    "negative",
    "ascent-override",
    "descent-override",
    "pad",
    "override-colors",
    "font-variant",
    "types",
    "system",
    "unicode-range",
    "syntax",
    "size-adjust",
    "speak-as",
    "font-style",
    "line-gap-override",
    "initial-value",
    "inherits",
    "font-display",
    "symbols",
    "base-palette",
    "additive-symbols",
    "prefix",
    "font-stretch",
    "font-feature-settings",
    "font-family",
    "fallback",
    "font-weight",
    "suffix",
    "-webkit-font-feature-settings"
  };
#define stringpool ((const char *) &stringpool_contents)
const struct Property *
AtRuleDescriptorHash::findDescriptorImpl (const char *str, unsigned int len)
{
  enum
    {
      TOTAL_KEYWORDS = 31,
      MIN_WORD_LENGTH = 3,
      MAX_WORD_LENGTH = 29,
      MIN_HASH_VALUE = 0,
      MAX_HASH_VALUE = 47
    };

  static const struct Property descriptor_word_list[] =
    {
//      {offsetof(struct stringpool_t, stringpool_str0), static_cast<int>(AtRuleDescriptorID::Src)},
//      {offsetof(struct stringpool_t, stringpool_str1), static_cast<int>(AtRuleDescriptorID::Navigation)},
//      {offsetof(struct stringpool_t, stringpool_str2), static_cast<int>(AtRuleDescriptorID::Range)},
//      {offsetof(struct stringpool_t, stringpool_str3), static_cast<int>(AtRuleDescriptorID::Negative)},
//      {offsetof(struct stringpool_t, stringpool_str4), static_cast<int>(AtRuleDescriptorID::AscentOverride)},
//      {offsetof(struct stringpool_t, stringpool_str5), static_cast<int>(AtRuleDescriptorID::DescentOverride)},
//      {offsetof(struct stringpool_t, stringpool_str6), static_cast<int>(AtRuleDescriptorID::Pad)},
//      {offsetof(struct stringpool_t, stringpool_str7), static_cast<int>(AtRuleDescriptorID::OverrideColors)},
//      {offsetof(struct stringpool_t, stringpool_str8), static_cast<int>(AtRuleDescriptorID::FontVariant)},
//      {offsetof(struct stringpool_t, stringpool_str9), static_cast<int>(AtRuleDescriptorID::Types)},
//      {offsetof(struct stringpool_t, stringpool_str10), static_cast<int>(AtRuleDescriptorID::System)},
//      {offsetof(struct stringpool_t, stringpool_str11), static_cast<int>(AtRuleDescriptorID::UnicodeRange)},
//      {offsetof(struct stringpool_t, stringpool_str12), static_cast<int>(AtRuleDescriptorID::Syntax)},
//      {offsetof(struct stringpool_t, stringpool_str13), static_cast<int>(AtRuleDescriptorID::SizeAdjust)},
//      {offsetof(struct stringpool_t, stringpool_str14), static_cast<int>(AtRuleDescriptorID::SpeakAs)},
//      {offsetof(struct stringpool_t, stringpool_str15), static_cast<int>(AtRuleDescriptorID::FontStyle)},
//      {offsetof(struct stringpool_t, stringpool_str16), static_cast<int>(AtRuleDescriptorID::LineGapOverride)},
//      {offsetof(struct stringpool_t, stringpool_str17), static_cast<int>(AtRuleDescriptorID::InitialValue)},
//      {offsetof(struct stringpool_t, stringpool_str18), static_cast<int>(AtRuleDescriptorID::Inherits)},
//      {offsetof(struct stringpool_t, stringpool_str19), static_cast<int>(AtRuleDescriptorID::FontDisplay)},
//      {offsetof(struct stringpool_t, stringpool_str20), static_cast<int>(AtRuleDescriptorID::Symbols)},
//      {offsetof(struct stringpool_t, stringpool_str21), static_cast<int>(AtRuleDescriptorID::BasePalette)},
//      {offsetof(struct stringpool_t, stringpool_str22), static_cast<int>(AtRuleDescriptorID::AdditiveSymbols)},
//      {offsetof(struct stringpool_t, stringpool_str23), static_cast<int>(AtRuleDescriptorID::Prefix)},
//      {offsetof(struct stringpool_t, stringpool_str24), static_cast<int>(AtRuleDescriptorID::FontStretch)},
//      {offsetof(struct stringpool_t, stringpool_str25), static_cast<int>(AtRuleDescriptorID::FontFeatureSettings)},
//      {offsetof(struct stringpool_t, stringpool_str26), static_cast<int>(AtRuleDescriptorID::FontFamily)},
//      {offsetof(struct stringpool_t, stringpool_str27), static_cast<int>(AtRuleDescriptorID::Fallback)},
//      {offsetof(struct stringpool_t, stringpool_str28), static_cast<int>(AtRuleDescriptorID::FontWeight)},
//      {offsetof(struct stringpool_t, stringpool_str29), static_cast<int>(AtRuleDescriptorID::Suffix)},
//      {offsetof(struct stringpool_t, stringpool_str30), static_cast<int>(AtRuleDescriptorID::FontFeatureSettings)}
    };

  static const signed char lookup[] =
    {
       0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13,
      14, 15, 16, 17, 18, -1, 19, 20, 21, 22, 23, -1, 24, 25,
      26, 27, 28, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
      -1, -1, -1, -1, -1, 30
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = descriptor_hash_function (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          int index = lookup[key];

          if (index >= 0)
            {
              const char *s = descriptor_word_list[index].name_offset + stringpool;

              if (*str == *s && !strncmp (str + 1, s + 1, len - 1) && s[len] == '\0')
                return &descriptor_word_list[index];
            }
        }
    }
  return 0;
}


const Property* FindDescriptor(const char* str, unsigned int len) {
  return AtRuleDescriptorHash::findDescriptorImpl(str, len);
}

template <typename CharacterType>
static AtRuleDescriptorID AsAtRuleDescriptorID(
    const CharacterType* descriptor_name,
    unsigned length) {
  if (length == 0)
    return AtRuleDescriptorID::Invalid;
  if (length > 29)
    return AtRuleDescriptorID::Invalid;

  char buffer[29 + 1];  // 1 for null character

  for (unsigned i = 0; i != length; ++i) {
    CharacterType c = descriptor_name[i];
    if (c == 0 || c >= 0x7F)
      return AtRuleDescriptorID::Invalid;  // illegal character
    buffer[i] = ToASCIILower(c);
  }
  buffer[length] = '\0';

  const char* name = buffer;
  const Property* hash_table_entry = FindDescriptor(name, length);
  if (!hash_table_entry)
    return AtRuleDescriptorID::Invalid;
  return static_cast<AtRuleDescriptorID>(hash_table_entry->id);
}

}  // namespace

AtRuleDescriptorID AsAtRuleDescriptorID(StringView string) {
  unsigned length = string.length();
  return string.Is8Bit()
             ? AsAtRuleDescriptorID(string.Characters8(), length)
             : AsAtRuleDescriptorID(string.Characters16(), length);
}

CSSPropertyID AtRuleDescriptorIDAsCSSPropertyID(AtRuleDescriptorID id) {
  switch (id) {
  case AtRuleDescriptorID::AdditiveSymbols:
    return CSSPropertyID::kAdditiveSymbols;
  case AtRuleDescriptorID::AscentOverride:
    return CSSPropertyID::kAscentOverride;
  case AtRuleDescriptorID::BasePalette:
    return CSSPropertyID::kBasePalette;
  case AtRuleDescriptorID::DescentOverride:
    return CSSPropertyID::kDescentOverride;
  case AtRuleDescriptorID::FontDisplay:
    return CSSPropertyID::kFontDisplay;
  case AtRuleDescriptorID::FontFamily:
    return CSSPropertyID::kFontFamily;
  case AtRuleDescriptorID::FontFeatureSettings:
    return CSSPropertyID::kFontFeatureSettings;
  case AtRuleDescriptorID::FontStretch:
    return CSSPropertyID::kFontStretch;
  case AtRuleDescriptorID::FontStyle:
    return CSSPropertyID::kFontStyle;
  case AtRuleDescriptorID::FontVariant:
    return CSSPropertyID::kFontVariant;
  case AtRuleDescriptorID::FontWeight:
    return CSSPropertyID::kFontWeight;
  case AtRuleDescriptorID::Inherits:
    return CSSPropertyID::kInherits;
  case AtRuleDescriptorID::InitialValue:
    return CSSPropertyID::kInitialValue;
  case AtRuleDescriptorID::Fallback:
    return CSSPropertyID::kFallback;
  case AtRuleDescriptorID::LineGapOverride:
    return CSSPropertyID::kLineGapOverride;
  case AtRuleDescriptorID::Negative:
    return CSSPropertyID::kNegative;
  case AtRuleDescriptorID::OverrideColors:
    return CSSPropertyID::kOverrideColors;
  case AtRuleDescriptorID::Pad:
    return CSSPropertyID::kPad;
  case AtRuleDescriptorID::Prefix:
    return CSSPropertyID::kPrefix;
  case AtRuleDescriptorID::Range:
    return CSSPropertyID::kRange;
  case AtRuleDescriptorID::Navigation:
    return CSSPropertyID::kNavigation;
  case AtRuleDescriptorID::Types:
    return CSSPropertyID::kTypes;
  case AtRuleDescriptorID::SizeAdjust:
    return CSSPropertyID::kSizeAdjust;
  case AtRuleDescriptorID::SpeakAs:
    return CSSPropertyID::kSpeakAs;
  case AtRuleDescriptorID::Src:
    return CSSPropertyID::kSrc;
  case AtRuleDescriptorID::Suffix:
    return CSSPropertyID::kSuffix;
  case AtRuleDescriptorID::Symbols:
    return CSSPropertyID::kSymbols;
  case AtRuleDescriptorID::Syntax:
    return CSSPropertyID::kSyntax;
  case AtRuleDescriptorID::System:
    return CSSPropertyID::kSystem;
  case AtRuleDescriptorID::UnicodeRange:
    return CSSPropertyID::kUnicodeRange;
  default:
    NOTREACHED();
    return CSSPropertyID::kInvalid;
  }
}

AtRuleDescriptorID CSSPropertyIDAsAtRuleDescriptor(CSSPropertyID id) {
  switch (id) {
  case CSSPropertyID::kAdditiveSymbols:
    return AtRuleDescriptorID::AdditiveSymbols;
  case CSSPropertyID::kAscentOverride:
    return AtRuleDescriptorID::AscentOverride;
  case CSSPropertyID::kBasePalette:
    return AtRuleDescriptorID::BasePalette;
  case CSSPropertyID::kDescentOverride:
    return AtRuleDescriptorID::DescentOverride;
  case CSSPropertyID::kFontDisplay:
    return AtRuleDescriptorID::FontDisplay;
  case CSSPropertyID::kFontFamily:
    return AtRuleDescriptorID::FontFamily;
  case CSSPropertyID::kFontFeatureSettings:
    return AtRuleDescriptorID::FontFeatureSettings;
  case CSSPropertyID::kFontStretch:
    return AtRuleDescriptorID::FontStretch;
  case CSSPropertyID::kFontStyle:
    return AtRuleDescriptorID::FontStyle;
  case CSSPropertyID::kFontVariant:
    return AtRuleDescriptorID::FontVariant;
  case CSSPropertyID::kFontWeight:
    return AtRuleDescriptorID::FontWeight;
  case CSSPropertyID::kInherits:
    return AtRuleDescriptorID::Inherits;
  case CSSPropertyID::kInitialValue:
    return AtRuleDescriptorID::InitialValue;
  case CSSPropertyID::kFallback:
    return AtRuleDescriptorID::Fallback;
  case CSSPropertyID::kLineGapOverride:
    return AtRuleDescriptorID::LineGapOverride;
  case CSSPropertyID::kNegative:
    return AtRuleDescriptorID::Negative;
  case CSSPropertyID::kOverrideColors:
    return AtRuleDescriptorID::OverrideColors;
  case CSSPropertyID::kPad:
    return AtRuleDescriptorID::Pad;
  case CSSPropertyID::kPrefix:
    return AtRuleDescriptorID::Prefix;
  case CSSPropertyID::kRange:
    return AtRuleDescriptorID::Range;
  case CSSPropertyID::kNavigation:
    return AtRuleDescriptorID::Navigation;
  case CSSPropertyID::kTypes:
    return AtRuleDescriptorID::Types;
  case CSSPropertyID::kSizeAdjust:
    return AtRuleDescriptorID::SizeAdjust;
  case CSSPropertyID::kSpeakAs:
    return AtRuleDescriptorID::SpeakAs;
  case CSSPropertyID::kSrc:
    return AtRuleDescriptorID::Src;
  case CSSPropertyID::kSuffix:
    return AtRuleDescriptorID::Suffix;
  case CSSPropertyID::kSymbols:
    return AtRuleDescriptorID::Symbols;
  case CSSPropertyID::kSyntax:
    return AtRuleDescriptorID::Syntax;
  case CSSPropertyID::kSystem:
    return AtRuleDescriptorID::System;
  case CSSPropertyID::kUnicodeRange:
    return AtRuleDescriptorID::UnicodeRange;
  default:
    return AtRuleDescriptorID::Invalid;
  }
}

}  // namespace blink
