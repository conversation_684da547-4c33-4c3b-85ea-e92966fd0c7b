// Generated by third_party/blink/renderer/build/scripts/gperf.py
/* C++ code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf --key-positions='*' -P -n -m 50 -D -Q CSSPropStringPool  */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif


// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "third_party/blink/renderer/core/css/css_property_names.h"

#include <string.h>
//#include "third_party/blink/public/mojom/use_counter/metrics/css_property_id.mojom-blink.h"
#include "third_party/blink/renderer/core/css/hash_tools.h"
#include "third_party/blink/renderer/core/css/parser/css_property_parser.h"
#include "third_party/blink/renderer/core/execution_context/execution_context.h"
//#include "third_party/blink/renderer/platform/runtime_enabled_features.h"
#include "third_party/blink/renderer/platform/wtf/text/ascii_ctype.h"
#include "third_party/blink/renderer/platform/wtf/text/atomic_string.h"
#include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"

#ifdef _MSC_VER
// Disable the warnings from casting a 64-bit pointer to 32-bit long
// warning C4302: 'type cast': truncation from 'char (*)[28]' to 'long'
// warning C4311: 'type cast': pointer truncation from 'char (*)[18]' to 'long'
#pragma warning(disable : 4302 4311)
#endif

namespace blink {
/* maximum key range = 4774, duplicates = 0 */

class CSSPropertyNamesHash
{
private:
  static inline unsigned int property_hash_function (const char *str, unsigned int len);
public:
  static const struct Property *findPropertyImpl (const char *str, unsigned int len);
};

inline unsigned int
CSSPropertyNamesHash::property_hash_function (const char *str, unsigned int len)
{
  static const unsigned short asso_values[] =
    {
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784,   10,   11, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784,   13,  148,   11,
        93,   10,  436,   32,  811,   10,  430,  360,   10,   19,
        11,   10,   17,   11,   10,   13,   11,  172, 1087,  340,
      1357, 1119,  637,   12, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784, 4784,
      4784, 4784, 4784, 4784, 4784, 4784, 4784
    };
  unsigned int hval = 0;

  switch (len)
    {
      default:
        hval += asso_values[(unsigned char)str[42]];
        [[fallthrough]];
      case 42:
        hval += asso_values[(unsigned char)str[41]];
        [[fallthrough]];
      case 41:
        hval += asso_values[(unsigned char)str[40]];
        [[fallthrough]];
      case 40:
        hval += asso_values[(unsigned char)str[39]];
        [[fallthrough]];
      case 39:
        hval += asso_values[(unsigned char)str[38]];
        [[fallthrough]];
      case 38:
        hval += asso_values[(unsigned char)str[37]];
        [[fallthrough]];
      case 37:
        hval += asso_values[(unsigned char)str[36]];
        [[fallthrough]];
      case 36:
        hval += asso_values[(unsigned char)str[35]];
        [[fallthrough]];
      case 35:
        hval += asso_values[(unsigned char)str[34]];
        [[fallthrough]];
      case 34:
        hval += asso_values[(unsigned char)str[33]];
        [[fallthrough]];
      case 33:
        hval += asso_values[(unsigned char)str[32]];
        [[fallthrough]];
      case 32:
        hval += asso_values[(unsigned char)str[31]];
        [[fallthrough]];
      case 31:
        hval += asso_values[(unsigned char)str[30]];
        [[fallthrough]];
      case 30:
        hval += asso_values[(unsigned char)str[29]];
        [[fallthrough]];
      case 29:
        hval += asso_values[(unsigned char)str[28]];
        [[fallthrough]];
      case 28:
        hval += asso_values[(unsigned char)str[27]];
        [[fallthrough]];
      case 27:
        hval += asso_values[(unsigned char)str[26]];
        [[fallthrough]];
      case 26:
        hval += asso_values[(unsigned char)str[25]];
        [[fallthrough]];
      case 25:
        hval += asso_values[(unsigned char)str[24]];
        [[fallthrough]];
      case 24:
        hval += asso_values[(unsigned char)str[23]];
        [[fallthrough]];
      case 23:
        hval += asso_values[(unsigned char)str[22]];
        [[fallthrough]];
      case 22:
        hval += asso_values[(unsigned char)str[21]];
        [[fallthrough]];
      case 21:
        hval += asso_values[(unsigned char)str[20]];
        [[fallthrough]];
      case 20:
        hval += asso_values[(unsigned char)str[19]];
        [[fallthrough]];
      case 19:
        hval += asso_values[(unsigned char)str[18]];
        [[fallthrough]];
      case 18:
        hval += asso_values[(unsigned char)str[17]];
        [[fallthrough]];
      case 17:
        hval += asso_values[(unsigned char)str[16]];
        [[fallthrough]];
      case 16:
        hval += asso_values[(unsigned char)str[15]+1];
        [[fallthrough]];
      case 15:
        hval += asso_values[(unsigned char)str[14]];
        [[fallthrough]];
      case 14:
        hval += asso_values[(unsigned char)str[13]];
        [[fallthrough]];
      case 13:
        hval += asso_values[(unsigned char)str[12]];
        [[fallthrough]];
      case 12:
        hval += asso_values[(unsigned char)str[11]];
        [[fallthrough]];
      case 11:
        hval += asso_values[(unsigned char)str[10]];
        [[fallthrough]];
      case 10:
        hval += asso_values[(unsigned char)str[9]];
        [[fallthrough]];
      case 9:
        hval += asso_values[(unsigned char)str[8]];
        [[fallthrough]];
      case 8:
        hval += asso_values[(unsigned char)str[7]];
        [[fallthrough]];
      case 7:
        hval += asso_values[(unsigned char)str[6]];
        [[fallthrough]];
      case 6:
        hval += asso_values[(unsigned char)str[5]];
        [[fallthrough]];
      case 5:
        hval += asso_values[(unsigned char)str[4]];
        [[fallthrough]];
      case 4:
        hval += asso_values[(unsigned char)str[3]];
        [[fallthrough]];
      case 3:
        hval += asso_values[(unsigned char)str[2]];
        [[fallthrough]];
      case 2:
        hval += asso_values[(unsigned char)str[1]];
        [[fallthrough]];
      case 1:
        hval += asso_values[(unsigned char)str[0]];
        break;
    }
  return hval;
}

struct CSSPropStringPool_t
  {
    char CSSPropStringPool_str0[sizeof("r")];
    char CSSPropStringPool_str1[sizeof("all")];
    char CSSPropStringPool_str2[sizeof("src")];
    char CSSPropStringPool_str3[sizeof("top")];
    char CSSPropStringPool_str4[sizeof("clip")];
    char CSSPropStringPool_str5[sizeof("color")];
    char CSSPropStringPool_str6[sizeof("clear")];
    char CSSPropStringPool_str7[sizeof("inset")];
    char CSSPropStringPool_str8[sizeof("scale")];
    char CSSPropStringPool_str9[sizeof("gap")];
    char CSSPropStringPool_str10[sizeof("rotate")];
    char CSSPropStringPool_str11[sizeof("page")];
    char CSSPropStringPool_str12[sizeof("content")];
    char CSSPropStringPool_str13[sizeof("range")];
    char CSSPropStringPool_str14[sizeof("contain")];
    char CSSPropStringPool_str15[sizeof("position")];
    char CSSPropStringPool_str16[sizeof("d")];
    char CSSPropStringPool_str17[sizeof("margin")];
    char CSSPropStringPool_str18[sizeof("container")];
    char CSSPropStringPool_str19[sizeof("isolation")];
    char CSSPropStringPool_str20[sizeof("translate")];
    char CSSPropStringPool_str21[sizeof("animation")];
    char CSSPropStringPool_str22[sizeof("transition")];
    char CSSPropStringPool_str23[sizeof("inset-area")];
    char CSSPropStringPool_str24[sizeof("stop-color")];
    char CSSPropStringPool_str25[sizeof("caret-color")];
    char CSSPropStringPool_str26[sizeof("line-clamp")];
    char CSSPropStringPool_str27[sizeof("pad")];
    char CSSPropStringPool_str28[sizeof("appearance")];
    char CSSPropStringPool_str29[sizeof("inset-inline")];
    char CSSPropStringPool_str30[sizeof("accent-color")];
    char CSSPropStringPool_str31[sizeof("scroll-start")];
    char CSSPropStringPool_str32[sizeof("order")];
    char CSSPropStringPool_str33[sizeof("place-items")];
    char CSSPropStringPool_str34[sizeof("aspect-ratio")];
    char CSSPropStringPool_str35[sizeof("app-region")];
    char CSSPropStringPool_str36[sizeof("margin-top")];
    char CSSPropStringPool_str37[sizeof("grid")];
    char CSSPropStringPool_str38[sizeof("place-content")];
    char CSSPropStringPool_str39[sizeof("initial-letter")];
    char CSSPropStringPool_str40[sizeof("align-items")];
    char CSSPropStringPool_str41[sizeof("container-name")];
    char CSSPropStringPool_str42[sizeof("align-content")];
    char CSSPropStringPool_str43[sizeof("timeline-scope")];
    char CSSPropStringPool_str44[sizeof("scroll-timeline")];
    char CSSPropStringPool_str45[sizeof("margin-inline")];
    char CSSPropStringPool_str46[sizeof("inset-inline-end")];
    char CSSPropStringPool_str47[sizeof("scroll-margin")];
    char CSSPropStringPool_str48[sizeof("animation-name")];
    char CSSPropStringPool_str49[sizeof("direction")];
    char CSSPropStringPool_str50[sizeof("letter-spacing")];
    char CSSPropStringPool_str51[sizeof("scroll-snap-stop")];
    char CSSPropStringPool_str52[sizeof("animation-range")];
    char CSSPropStringPool_str53[sizeof("page-orientation")];
    char CSSPropStringPool_str54[sizeof("grid-area")];
    char CSSPropStringPool_str55[sizeof("paint-order")];
    char CSSPropStringPool_str56[sizeof("bottom")];
    char CSSPropStringPool_str57[sizeof("scroll-start-inline")];
    char CSSPropStringPool_str58[sizeof("grid-gap")];
    char CSSPropStringPool_str59[sizeof("image-orientation")];
    char CSSPropStringPool_str60[sizeof("caption-side")];
    char CSSPropStringPool_str61[sizeof("scroll-margin-top")];
    char CSSPropStringPool_str62[sizeof("cursor")];
    char CSSPropStringPool_str63[sizeof("quotes")];
    char CSSPropStringPool_str64[sizeof("scroll-timeline-name")];
    char CSSPropStringPool_str65[sizeof("scroll-start-target")];
    char CSSPropStringPool_str66[sizeof("outline")];
    char CSSPropStringPool_str67[sizeof("scroll-margin-inline")];
    char CSSPropStringPool_str68[sizeof("columns")];
    char CSSPropStringPool_str69[sizeof("animation-composition")];
    char CSSPropStringPool_str70[sizeof("grid-template")];
    char CSSPropStringPool_str71[sizeof("color-rendering")];
    char CSSPropStringPool_str72[sizeof("clip-rule")];
    char CSSPropStringPool_str73[sizeof("animation-range-start")];
    char CSSPropStringPool_str74[sizeof("padding")];
    char CSSPropStringPool_str75[sizeof("base-palette")];
    char CSSPropStringPool_str76[sizeof("counter-set")];
    char CSSPropStringPool_str77[sizeof("user-select")];
    char CSSPropStringPool_str78[sizeof("border")];
    char CSSPropStringPool_str79[sizeof("margin-inline-end")];
    char CSSPropStringPool_str80[sizeof("image-rendering")];
    char CSSPropStringPool_str81[sizeof("outline-color")];
    char CSSPropStringPool_str82[sizeof("scrollbar-color")];
    char CSSPropStringPool_str83[sizeof("column-span")];
    char CSSPropStringPool_str84[sizeof("counter-reset")];
    char CSSPropStringPool_str85[sizeof("scroll-start-target-inline")];
    char CSSPropStringPool_str86[sizeof("column-gap")];
    char CSSPropStringPool_str87[sizeof("scroll-margin-inline-start")];
    char CSSPropStringPool_str88[sizeof("margin-bottom")];
    char CSSPropStringPool_str89[sizeof("padding-top")];
    char CSSPropStringPool_str90[sizeof("animation-range-end")];
    char CSSPropStringPool_str91[sizeof("grid-template-areas")];
    char CSSPropStringPool_str92[sizeof("border-top")];
    char CSSPropStringPool_str93[sizeof("inset-inline-start")];
    char CSSPropStringPool_str94[sizeof("padding-inline")];
    char CSSPropStringPool_str95[sizeof("border-color")];
    char CSSPropStringPool_str96[sizeof("scroll-padding")];
    char CSSPropStringPool_str97[sizeof("counter-increment")];
    char CSSPropStringPool_str98[sizeof("border-inline")];
    char CSSPropStringPool_str99[sizeof("scroll-margin-inline-end")];
    char CSSPropStringPool_str100[sizeof("color-interpolation")];
    char CSSPropStringPool_str101[sizeof("border-image")];
    char CSSPropStringPool_str102[sizeof("border-collapse")];
    char CSSPropStringPool_str103[sizeof("grid-column")];
    char CSSPropStringPool_str104[sizeof("border-top-color")];
    char CSSPropStringPool_str105[sizeof("scroll-margin-bottom")];
    char CSSPropStringPool_str106[sizeof("margin-inline-start")];
    char CSSPropStringPool_str107[sizeof("border-spacing")];
    char CSSPropStringPool_str108[sizeof("unicode-range")];
    char CSSPropStringPool_str109[sizeof("mask")];
    char CSSPropStringPool_str110[sizeof("padding-inline-start")];
    char CSSPropStringPool_str111[sizeof("dominant-baseline")];
    char CSSPropStringPool_str112[sizeof("speak")];
    char CSSPropStringPool_str113[sizeof("stroke")];
    char CSSPropStringPool_str114[sizeof("border-inline-color")];
    char CSSPropStringPool_str115[sizeof("marker")];
    char CSSPropStringPool_str116[sizeof("scroll-padding-bottom")];
    char CSSPropStringPool_str117[sizeof("row-gap")];
    char CSSPropStringPool_str118[sizeof("column-rule")];
    char CSSPropStringPool_str119[sizeof("speak-as")];
    char CSSPropStringPool_str120[sizeof("border-image-repeat")];
    char CSSPropStringPool_str121[sizeof("animation-direction")];
    char CSSPropStringPool_str122[sizeof("column-count")];
    char CSSPropStringPool_str123[sizeof("grid-column-start")];
    char CSSPropStringPool_str124[sizeof("grid-column-gap")];
    char CSSPropStringPool_str125[sizeof("baseline-source")];
    char CSSPropStringPool_str126[sizeof("mask-clip")];
    char CSSPropStringPool_str127[sizeof("fill")];
    char CSSPropStringPool_str128[sizeof("left")];
    char CSSPropStringPool_str129[sizeof("font")];
    char CSSPropStringPool_str130[sizeof("border-inline-end")];
    char CSSPropStringPool_str131[sizeof("float")];
    char CSSPropStringPool_str132[sizeof("mask-repeat")];
    char CSSPropStringPool_str133[sizeof("filter")];
    char CSSPropStringPool_str134[sizeof("padding-bottom")];
    char CSSPropStringPool_str135[sizeof("marker-start")];
    char CSSPropStringPool_str136[sizeof("scrollbar-gutter")];
    char CSSPropStringPool_str137[sizeof("mask-origin")];
    char CSSPropStringPool_str138[sizeof("mask-image")];
    char CSSPropStringPool_str139[sizeof("border-bottom")];
    char CSSPropStringPool_str140[sizeof("stroke-linecap")];
    char CSSPropStringPool_str141[sizeof("mask-position")];
    char CSSPropStringPool_str142[sizeof("grid-column-end")];
    char CSSPropStringPool_str143[sizeof("column-rule-color")];
    char CSSPropStringPool_str144[sizeof("grid-row")];
    char CSSPropStringPool_str145[sizeof("grid-template-columns")];
    char CSSPropStringPool_str146[sizeof("mask-composite")];
    char CSSPropStringPool_str147[sizeof("transform")];
    char CSSPropStringPool_str148[sizeof("border-inline-end-color")];
    char CSSPropStringPool_str149[sizeof("place-self")];
    char CSSPropStringPool_str150[sizeof("marker-end")];
    char CSSPropStringPool_str151[sizeof("mask-mode")];
    char CSSPropStringPool_str152[sizeof("scroll-padding-top")];
    char CSSPropStringPool_str153[sizeof("marker-mid")];
    char CSSPropStringPool_str154[sizeof("align-self")];
    char CSSPropStringPool_str155[sizeof("reading-order-items")];
    char CSSPropStringPool_str156[sizeof("font-palette")];
    char CSSPropStringPool_str157[sizeof("writing-mode")];
    char CSSPropStringPool_str158[sizeof("border-bottom-color")];
    char CSSPropStringPool_str159[sizeof("word-spacing")];
    char CSSPropStringPool_str160[sizeof("margin-left")];
    char CSSPropStringPool_str161[sizeof("border-inline-start")];
    char CSSPropStringPool_str162[sizeof("grid-row-start")];
    char CSSPropStringPool_str163[sizeof("grid-row-gap")];
    char CSSPropStringPool_str164[sizeof("unicode-bidi")];
    char CSSPropStringPool_str165[sizeof("line-break")];
    char CSSPropStringPool_str166[sizeof("animation-iteration-count")];
    char CSSPropStringPool_str167[sizeof("border-radius")];
    char CSSPropStringPool_str168[sizeof("inset-block")];
    char CSSPropStringPool_str169[sizeof("transition-duration")];
    char CSSPropStringPool_str170[sizeof("grid-auto-columns")];
    char CSSPropStringPool_str171[sizeof("flood-color")];
    char CSSPropStringPool_str172[sizeof("transform-origin")];
    char CSSPropStringPool_str173[sizeof("animation-timeline")];
    char CSSPropStringPool_str174[sizeof("grid-row-end")];
    char CSSPropStringPool_str175[sizeof("border-inline-start-color")];
    char CSSPropStringPool_str176[sizeof("margin-block")];
    char CSSPropStringPool_str177[sizeof("grid-template-rows")];
    char CSSPropStringPool_str178[sizeof("-internal-forced-color")];
    char CSSPropStringPool_str179[sizeof("font-optical-sizing")];
    char CSSPropStringPool_str180[sizeof("size")];
    char CSSPropStringPool_str181[sizeof("inset-block-start")];
    char CSSPropStringPool_str182[sizeof("zoom")];
    char CSSPropStringPool_str183[sizeof("fill-rule")];
    char CSSPropStringPool_str184[sizeof("margin-block-end")];
    char CSSPropStringPool_str185[sizeof("scroll-start-block")];
    char CSSPropStringPool_str186[sizeof("resize")];
    char CSSPropStringPool_str187[sizeof("page-break-inside")];
    char CSSPropStringPool_str188[sizeof("break-inside")];
    char CSSPropStringPool_str189[sizeof("column-fill")];
    char CSSPropStringPool_str190[sizeof("border-start-end-radius")];
    char CSSPropStringPool_str191[sizeof("animation-fill-mode")];
    char CSSPropStringPool_str192[sizeof("object-position")];
    char CSSPropStringPool_str193[sizeof("scroll-margin-block")];
    char CSSPropStringPool_str194[sizeof("inset-block-end")];
    char CSSPropStringPool_str195[sizeof("inline-size")];
    char CSSPropStringPool_str196[sizeof("grid-auto-rows")];
    char CSSPropStringPool_str197[sizeof("padding-left")];
    char CSSPropStringPool_str198[sizeof("scroll-padding-block")];
    char CSSPropStringPool_str199[sizeof("border-left")];
    char CSSPropStringPool_str200[sizeof("border-image-outset")];
    char CSSPropStringPool_str201[sizeof("scroll-start-target-block")];
    char CSSPropStringPool_str202[sizeof("alignment-baseline")];
    char CSSPropStringPool_str203[sizeof("min-inline-size")];
    char CSSPropStringPool_str204[sizeof("scroll-margin-block-start")];
    char CSSPropStringPool_str205[sizeof("border-top-width")];
    char CSSPropStringPool_str206[sizeof("widows")];
    char CSSPropStringPool_str207[sizeof("-internal-align-content-block")];
    char CSSPropStringPool_str208[sizeof("padding-block")];
    char CSSPropStringPool_str209[sizeof("scroll-padding-block-start")];
    char CSSPropStringPool_str210[sizeof("border-left-color")];
    char CSSPropStringPool_str211[sizeof("scroll-padding-left")];
    char CSSPropStringPool_str212[sizeof("border-block")];
    char CSSPropStringPool_str213[sizeof("scroll-padding-inline")];
    char CSSPropStringPool_str214[sizeof("word-wrap")];
    char CSSPropStringPool_str215[sizeof("margin-block-start")];
    char CSSPropStringPool_str216[sizeof("scroll-margin-block-end")];
    char CSSPropStringPool_str217[sizeof("tab-size")];
    char CSSPropStringPool_str218[sizeof("border-end-end-radius")];
    char CSSPropStringPool_str219[sizeof("border-image-slice")];
    char CSSPropStringPool_str220[sizeof("background")];
    char CSSPropStringPool_str221[sizeof("animation-duration")];
    char CSSPropStringPool_str222[sizeof("border-block-end")];
    char CSSPropStringPool_str223[sizeof("border-start-start-radius")];
    char CSSPropStringPool_str224[sizeof("right")];
    char CSSPropStringPool_str225[sizeof("color-interpolation-filters")];
    char CSSPropStringPool_str226[sizeof("scroll-padding-block-end")];
    char CSSPropStringPool_str227[sizeof("transition-delay")];
    char CSSPropStringPool_str228[sizeof("orphans")];
    char CSSPropStringPool_str229[sizeof("inherits")];
    char CSSPropStringPool_str230[sizeof("padding-inline-end")];
    char CSSPropStringPool_str231[sizeof("transition-timing-function")];
    char CSSPropStringPool_str232[sizeof("-internal-forced-outline-color")];
    char CSSPropStringPool_str233[sizeof("border-block-color")];
    char CSSPropStringPool_str234[sizeof("scroll-padding-inline-start")];
    char CSSPropStringPool_str235[sizeof("clip-path")];
    char CSSPropStringPool_str236[sizeof("offset")];
    char CSSPropStringPool_str237[sizeof("background-clip")];
    char CSSPropStringPool_str238[sizeof("font-kerning")];
    char CSSPropStringPool_str239[sizeof("background-color")];
    char CSSPropStringPool_str240[sizeof("stroke-linejoin")];
    char CSSPropStringPool_str241[sizeof("anchor-name")];
    char CSSPropStringPool_str242[sizeof("border-block-end-color")];
    char CSSPropStringPool_str243[sizeof("color-scheme")];
    char CSSPropStringPool_str244[sizeof("-epub-writing-mode")];
    char CSSPropStringPool_str245[sizeof("padding-block-end")];
    char CSSPropStringPool_str246[sizeof("-internal-forced-border-color")];
    char CSSPropStringPool_str247[sizeof("border-end-start-radius")];
    char CSSPropStringPool_str248[sizeof("scroll-padding-inline-end")];
    char CSSPropStringPool_str249[sizeof("-webkit-locale")];
    char CSSPropStringPool_str250[sizeof("stroke-miterlimit")];
    char CSSPropStringPool_str251[sizeof("position-anchor")];
    char CSSPropStringPool_str252[sizeof("shape-margin")];
    char CSSPropStringPool_str253[sizeof("margin-right")];
    char CSSPropStringPool_str254[sizeof("lighting-color")];
    char CSSPropStringPool_str255[sizeof("offset-rotate")];
    char CSSPropStringPool_str256[sizeof("scroll-snap-align")];
    char CSSPropStringPool_str257[sizeof("fallback")];
    char CSSPropStringPool_str258[sizeof("word-break")];
    char CSSPropStringPool_str259[sizeof("-epub-caption-side")];
    char CSSPropStringPool_str260[sizeof("-webkit-animation")];
    char CSSPropStringPool_str261[sizeof("offset-position")];
    char CSSPropStringPool_str262[sizeof("-epub-word-break")];
    char CSSPropStringPool_str263[sizeof("-webkit-appearance")];
    char CSSPropStringPool_str264[sizeof("break-after")];
    char CSSPropStringPool_str265[sizeof("-webkit-order")];
    char CSSPropStringPool_str266[sizeof("border-block-start")];
    char CSSPropStringPool_str267[sizeof("padding-block-start")];
    char CSSPropStringPool_str268[sizeof("-webkit-margin-start")];
    char CSSPropStringPool_str269[sizeof("-webkit-align-content")];
    char CSSPropStringPool_str270[sizeof("shape-rendering")];
    char CSSPropStringPool_str271[sizeof("scroll-margin-left")];
    char CSSPropStringPool_str272[sizeof("background-repeat")];
    char CSSPropStringPool_str273[sizeof("-webkit-animation-name")];
    char CSSPropStringPool_str274[sizeof("mask-size")];
    char CSSPropStringPool_str275[sizeof("object-fit")];
    char CSSPropStringPool_str276[sizeof("touch-action")];
    char CSSPropStringPool_str277[sizeof("border-block-start-color")];
    char CSSPropStringPool_str278[sizeof("offset-distance")];
    char CSSPropStringPool_str279[sizeof("page-break-after")];
    char CSSPropStringPool_str280[sizeof("y")];
    char CSSPropStringPool_str281[sizeof("background-position")];
    char CSSPropStringPool_str282[sizeof("border-top-left-radius")];
    char CSSPropStringPool_str283[sizeof("ry")];
    char CSSPropStringPool_str284[sizeof("cy")];
    char CSSPropStringPool_str285[sizeof("-webkit-columns")];
    char CSSPropStringPool_str286[sizeof("font-size")];
    char CSSPropStringPool_str287[sizeof("padding-right")];
    char CSSPropStringPool_str288[sizeof("-webkit-line-clamp")];
    char CSSPropStringPool_str289[sizeof("backdrop-filter")];
    char CSSPropStringPool_str290[sizeof("outline-offset")];
    char CSSPropStringPool_str291[sizeof("border-right")];
    char CSSPropStringPool_str292[sizeof("grid-auto-flow")];
    char CSSPropStringPool_str293[sizeof("types")];
    char CSSPropStringPool_str294[sizeof("break-before")];
    char CSSPropStringPool_str295[sizeof("negative")];
    char CSSPropStringPool_str296[sizeof("system")];
    char CSSPropStringPool_str297[sizeof("-webkit-user-select")];
    char CSSPropStringPool_str298[sizeof("opacity")];
    char CSSPropStringPool_str299[sizeof("shape-outside")];
    char CSSPropStringPool_str300[sizeof("-webkit-column-span")];
    char CSSPropStringPool_str301[sizeof("-webkit-animation-direction")];
    char CSSPropStringPool_str302[sizeof("background-blend-mode")];
    char CSSPropStringPool_str303[sizeof("perspective")];
    char CSSPropStringPool_str304[sizeof("navigation")];
    char CSSPropStringPool_str305[sizeof("-webkit-align-items")];
    char CSSPropStringPool_str306[sizeof("list-style")];
    char CSSPropStringPool_str307[sizeof("block-size")];
    char CSSPropStringPool_str308[sizeof("scroll-padding-right")];
    char CSSPropStringPool_str309[sizeof("pointer-events")];
    char CSSPropStringPool_str310[sizeof("border-right-color")];
    char CSSPropStringPool_str311[sizeof("-webkit-padding-start")];
    char CSSPropStringPool_str312[sizeof("empty-cells")];
    char CSSPropStringPool_str313[sizeof("position-try")];
    char CSSPropStringPool_str314[sizeof("-webkit-border-start")];
    char CSSPropStringPool_str315[sizeof("vertical-align")];
    char CSSPropStringPool_str316[sizeof("stop-opacity")];
    char CSSPropStringPool_str317[sizeof("white-space")];
    char CSSPropStringPool_str318[sizeof("page-break-before")];
    char CSSPropStringPool_str319[sizeof("scroll-start-y")];
    char CSSPropStringPool_str320[sizeof("container-type")];
    char CSSPropStringPool_str321[sizeof("width")];
    char CSSPropStringPool_str322[sizeof("will-change")];
    char CSSPropStringPool_str323[sizeof("min-block-size")];
    char CSSPropStringPool_str324[sizeof("display")];
    char CSSPropStringPool_str325[sizeof("line-gap-override")];
    char CSSPropStringPool_str326[sizeof("field-sizing")];
    char CSSPropStringPool_str327[sizeof("contain-intrinsic-size")];
    char CSSPropStringPool_str328[sizeof("-webkit-padding-end")];
    char CSSPropStringPool_str329[sizeof("position-try-order")];
    char CSSPropStringPool_str330[sizeof("forced-color-adjust")];
    char CSSPropStringPool_str331[sizeof("-webkit-mask")];
    char CSSPropStringPool_str332[sizeof("-webkit-border-start-color")];
    char CSSPropStringPool_str333[sizeof("override-colors")];
    char CSSPropStringPool_str334[sizeof("min-width")];
    char CSSPropStringPool_str335[sizeof("ascent-override")];
    char CSSPropStringPool_str336[sizeof("symbols")];
    char CSSPropStringPool_str337[sizeof("animation-play-state")];
    char CSSPropStringPool_str338[sizeof("-webkit-animation-iteration-count")];
    char CSSPropStringPool_str339[sizeof("-webkit-column-rule")];
    char CSSPropStringPool_str340[sizeof("-webkit-animation-duration")];
    char CSSPropStringPool_str341[sizeof("font-stretch")];
    char CSSPropStringPool_str342[sizeof("contain-intrinsic-inline-size")];
    char CSSPropStringPool_str343[sizeof("x")];
    char CSSPropStringPool_str344[sizeof("-webkit-mask-size")];
    char CSSPropStringPool_str345[sizeof("scroll-start-target-y")];
    char CSSPropStringPool_str346[sizeof("animation-delay")];
    char CSSPropStringPool_str347[sizeof("rx")];
    char CSSPropStringPool_str348[sizeof("cx")];
    char CSSPropStringPool_str349[sizeof("white-space-collapse")];
    char CSSPropStringPool_str350[sizeof("background-origin")];
    char CSSPropStringPool_str351[sizeof("initial-value")];
    char CSSPropStringPool_str352[sizeof("-webkit-mask-repeat")];
    char CSSPropStringPool_str353[sizeof("background-image")];
    char CSSPropStringPool_str354[sizeof("-webkit-filter")];
    char CSSPropStringPool_str355[sizeof("-webkit-user-drag")];
    char CSSPropStringPool_str356[sizeof("-internal-font-size-delta")];
    char CSSPropStringPool_str357[sizeof("-webkit-mask-position")];
    char CSSPropStringPool_str358[sizeof("outline-style")];
    char CSSPropStringPool_str359[sizeof("-webkit-column-rule-color")];
    char CSSPropStringPool_str360[sizeof("size-adjust")];
    char CSSPropStringPool_str361[sizeof("origin-trial-test-property")];
    char CSSPropStringPool_str362[sizeof("-webkit-mask-composite")];
    char CSSPropStringPool_str363[sizeof("font-feature-settings")];
    char CSSPropStringPool_str364[sizeof("-webkit-transition")];
    char CSSPropStringPool_str365[sizeof("-webkit-transform")];
    char CSSPropStringPool_str366[sizeof("-webkit-column-count")];
    char CSSPropStringPool_str367[sizeof("border-style")];
    char CSSPropStringPool_str368[sizeof("-webkit-app-region")];
    char CSSPropStringPool_str369[sizeof("-webkit-writing-mode")];
    char CSSPropStringPool_str370[sizeof("transition-behavior")];
    char CSSPropStringPool_str371[sizeof("scroll-margin-right")];
    char CSSPropStringPool_str372[sizeof("text-align")];
    char CSSPropStringPool_str373[sizeof("list-style-position")];
    char CSSPropStringPool_str374[sizeof("-webkit-margin-before")];
    char CSSPropStringPool_str375[sizeof("position-try-options")];
    char CSSPropStringPool_str376[sizeof("scroll-start-x")];
    char CSSPropStringPool_str377[sizeof("-webkit-border-radius")];
    char CSSPropStringPool_str378[sizeof("text-spacing")];
    char CSSPropStringPool_str379[sizeof("column-width")];
    char CSSPropStringPool_str380[sizeof("outline-width")];
    char CSSPropStringPool_str381[sizeof("scrollbar-width")];
    char CSSPropStringPool_str382[sizeof("text-orientation")];
    char CSSPropStringPool_str383[sizeof("baseline-shift")];
    char CSSPropStringPool_str384[sizeof("border-block-style")];
    char CSSPropStringPool_str385[sizeof("-internal-forced-background-color")];
    char CSSPropStringPool_str386[sizeof("border-image-source")];
    char CSSPropStringPool_str387[sizeof("-webkit-transform-origin")];
    char CSSPropStringPool_str388[sizeof("text-align-last")];
    char CSSPropStringPool_str389[sizeof("-webkit-mask-image")];
    char CSSPropStringPool_str390[sizeof("table-layout")];
    char CSSPropStringPool_str391[sizeof("background-size")];
    char CSSPropStringPool_str392[sizeof("-webkit-margin-end")];
    char CSSPropStringPool_str393[sizeof("text-indent")];
    char CSSPropStringPool_str394[sizeof("view-timeline")];
    char CSSPropStringPool_str395[sizeof("ruby-position")];
    char CSSPropStringPool_str396[sizeof("-webkit-rtl-ordering")];
    char CSSPropStringPool_str397[sizeof("border-width")];
    char CSSPropStringPool_str398[sizeof("border-image-width")];
    char CSSPropStringPool_str399[sizeof("scroll-timeline-axis")];
    char CSSPropStringPool_str400[sizeof("mask-type")];
    char CSSPropStringPool_str401[sizeof("text-decoration")];
    char CSSPropStringPool_str402[sizeof("text-rendering")];
    char CSSPropStringPool_str403[sizeof("scroll-start-target-x")];
    char CSSPropStringPool_str404[sizeof("view-timeline-inset")];
    char CSSPropStringPool_str405[sizeof("stroke-opacity")];
    char CSSPropStringPool_str406[sizeof("-webkit-margin-after")];
    char CSSPropStringPool_str407[sizeof("column-rule-style")];
    char CSSPropStringPool_str408[sizeof("view-transition-name")];
    char CSSPropStringPool_str409[sizeof("-webkit-animation-fill-mode")];
    char CSSPropStringPool_str410[sizeof("font-variant")];
    char CSSPropStringPool_str411[sizeof("view-transition-class")];
    char CSSPropStringPool_str412[sizeof("text-decoration-line")];
    char CSSPropStringPool_str413[sizeof("font-style")];
    char CSSPropStringPool_str414[sizeof("border-inline-end-style")];
    char CSSPropStringPool_str415[sizeof("text-decoration-color")];
    char CSSPropStringPool_str416[sizeof("-webkit-padding-after")];
    char CSSPropStringPool_str417[sizeof("fill-opacity")];
    char CSSPropStringPool_str418[sizeof("text-autospace")];
    char CSSPropStringPool_str419[sizeof("animation-timing-function")];
    char CSSPropStringPool_str420[sizeof("-webkit-border-before")];
    char CSSPropStringPool_str421[sizeof("height")];
    char CSSPropStringPool_str422[sizeof("border-inline-style")];
    char CSSPropStringPool_str423[sizeof("stroke-width")];
    char CSSPropStringPool_str424[sizeof("font-variant-caps")];
    char CSSPropStringPool_str425[sizeof("font-weight")];
    char CSSPropStringPool_str426[sizeof("-webkit-border-image")];
    char CSSPropStringPool_str427[sizeof("border-top-right-radius")];
    char CSSPropStringPool_str428[sizeof("-webkit-column-break-inside")];
    char CSSPropStringPool_str429[sizeof("transform-style")];
    char CSSPropStringPool_str430[sizeof("scroll-snap-type")];
    char CSSPropStringPool_str431[sizeof("-webkit-border-vertical-spacing")];
    char CSSPropStringPool_str432[sizeof("border-bottom-left-radius")];
    char CSSPropStringPool_str433[sizeof("-webkit-border-end")];
    char CSSPropStringPool_str434[sizeof("font-variant-position")];
    char CSSPropStringPool_str435[sizeof("min-height")];
    char CSSPropStringPool_str436[sizeof("line-height")];
    char CSSPropStringPool_str437[sizeof("list-style-image")];
    char CSSPropStringPool_str438[sizeof("-webkit-border-before-color")];
    char CSSPropStringPool_str439[sizeof("view-timeline-name")];
    char CSSPropStringPool_str440[sizeof("transition-property")];
    char CSSPropStringPool_str441[sizeof("border-inline-end-width")];
    char CSSPropStringPool_str442[sizeof("font-display")];
    char CSSPropStringPool_str443[sizeof("border-inline-start-style")];
    char CSSPropStringPool_str444[sizeof("font-variant-east-asian")];
    char CSSPropStringPool_str445[sizeof("flood-opacity")];
    char CSSPropStringPool_str446[sizeof("-webkit-print-color-adjust")];
    char CSSPropStringPool_str447[sizeof("-webkit-transition-duration")];
    char CSSPropStringPool_str448[sizeof("font-variation-settings")];
    char CSSPropStringPool_str449[sizeof("offset-path")];
    char CSSPropStringPool_str450[sizeof("text-wrap")];
    char CSSPropStringPool_str451[sizeof("-webkit-mask-clip")];
    char CSSPropStringPool_str452[sizeof("-webkit-border-end-color")];
    char CSSPropStringPool_str453[sizeof("offset-anchor")];
    char CSSPropStringPool_str454[sizeof("-webkit-animation-timing-function")];
    char CSSPropStringPool_str455[sizeof("-webkit-padding-before")];
    char CSSPropStringPool_str456[sizeof("-webkit-border-after")];
    char CSSPropStringPool_str457[sizeof("math-depth")];
    char CSSPropStringPool_str458[sizeof("mix-blend-mode")];
    char CSSPropStringPool_str459[sizeof("flex")];
    char CSSPropStringPool_str460[sizeof("-webkit-mask-origin")];
    char CSSPropStringPool_str461[sizeof("-internal-visited-color")];
    char CSSPropStringPool_str462[sizeof("text-underline-position")];
    char CSSPropStringPool_str463[sizeof("contain-intrinsic-block-size")];
    char CSSPropStringPool_str464[sizeof("border-bottom-style")];
    char CSSPropStringPool_str465[sizeof("descent-override")];
    char CSSPropStringPool_str466[sizeof("prefix")];
    char CSSPropStringPool_str467[sizeof("dynamic-range-limit")];
    char CSSPropStringPool_str468[sizeof("border-right-style")];
    char CSSPropStringPool_str469[sizeof("border-inline-start-width")];
    char CSSPropStringPool_str470[sizeof("-webkit-border-after-color")];
    char CSSPropStringPool_str471[sizeof("background-attachment")];
    char CSSPropStringPool_str472[sizeof("font-variant-numeric")];
    char CSSPropStringPool_str473[sizeof("contain-intrinsic-width")];
    char CSSPropStringPool_str474[sizeof("-webkit-align-self")];
    char CSSPropStringPool_str475[sizeof("column-rule-width")];
    char CSSPropStringPool_str476[sizeof("-epub-text-orientation")];
    char CSSPropStringPool_str477[sizeof("-internal-visited-caret-color")];
    char CSSPropStringPool_str478[sizeof("-internal-forced-visited-color")];
    char CSSPropStringPool_str479[sizeof("overflow")];
    char CSSPropStringPool_str480[sizeof("font-variant-alternates")];
    char CSSPropStringPool_str481[sizeof("-webkit-line-break")];
    char CSSPropStringPool_str482[sizeof("border-top-style")];
    char CSSPropStringPool_str483[sizeof("text-transform")];
    char CSSPropStringPool_str484[sizeof("border-left-style")];
    char CSSPropStringPool_str485[sizeof("buffered-rendering")];
    char CSSPropStringPool_str486[sizeof("-webkit-clip-path")];
    char CSSPropStringPool_str487[sizeof("-webkit-column-gap")];
    char CSSPropStringPool_str488[sizeof("overflow-inline")];
    char CSSPropStringPool_str489[sizeof("text-spacing-trim")];
    char CSSPropStringPool_str490[sizeof("flex-direction")];
    char CSSPropStringPool_str491[sizeof("-webkit-shape-margin")];
    char CSSPropStringPool_str492[sizeof("flex-basis")];
    char CSSPropStringPool_str493[sizeof("border-block-width")];
    char CSSPropStringPool_str494[sizeof("math-style")];
    char CSSPropStringPool_str495[sizeof("-webkit-column-break-after")];
    char CSSPropStringPool_str496[sizeof("stroke-dasharray")];
    char CSSPropStringPool_str497[sizeof("-webkit-border-horizontal-spacing")];
    char CSSPropStringPool_str498[sizeof("border-block-end-style")];
    char CSSPropStringPool_str499[sizeof("border-inline-width")];
    char CSSPropStringPool_str500[sizeof("font-size-adjust")];
    char CSSPropStringPool_str501[sizeof("transform-box")];
    char CSSPropStringPool_str502[sizeof("-webkit-border-bottom-left-radius")];
    char CSSPropStringPool_str503[sizeof("vector-effect")];
    char CSSPropStringPool_str504[sizeof("-internal-visited-outline-color")];
    char CSSPropStringPool_str505[sizeof("perspective-origin")];
    char CSSPropStringPool_str506[sizeof("font-family")];
    char CSSPropStringPool_str507[sizeof("-webkit-opacity")];
    char CSSPropStringPool_str508[sizeof("-internal-overflow-inline")];
    char CSSPropStringPool_str509[sizeof("border-bottom-right-radius")];
    char CSSPropStringPool_str510[sizeof("z-index")];
    char CSSPropStringPool_str511[sizeof("font-variant-emoji")];
    char CSSPropStringPool_str512[sizeof("max-inline-size")];
    char CSSPropStringPool_str513[sizeof("math-shift")];
    char CSSPropStringPool_str514[sizeof("border-block-end-width")];
    char CSSPropStringPool_str515[sizeof("-internal-visited-border-top-color")];
    char CSSPropStringPool_str516[sizeof("scroll-behavior")];
    char CSSPropStringPool_str517[sizeof("-webkit-transform-origin-z")];
    char CSSPropStringPool_str518[sizeof("-webkit-column-break-before")];
    char CSSPropStringPool_str519[sizeof("-webkit-border-top-left-radius")];
    char CSSPropStringPool_str520[sizeof("-internal-visited-stroke")];
    char CSSPropStringPool_str521[sizeof("border-left-width")];
    char CSSPropStringPool_str522[sizeof("border-bottom-width")];
    char CSSPropStringPool_str523[sizeof("flex-wrap")];
    char CSSPropStringPool_str524[sizeof("border-block-start-style")];
    char CSSPropStringPool_str525[sizeof("overflow-clip-margin")];
    char CSSPropStringPool_str526[sizeof("-webkit-transition-timing-function")];
    char CSSPropStringPool_str527[sizeof("flex-grow")];
    char CSSPropStringPool_str528[sizeof("box-sizing")];
    char CSSPropStringPool_str529[sizeof("-internal-visited-fill")];
    char CSSPropStringPool_str530[sizeof("-webkit-animation-play-state")];
    char CSSPropStringPool_str531[sizeof("background-position-y")];
    char CSSPropStringPool_str532[sizeof("-internal-visited-border-inline-start-color")];
    char CSSPropStringPool_str533[sizeof("overlay")];
    char CSSPropStringPool_str534[sizeof("justify-items")];
    char CSSPropStringPool_str535[sizeof("text-anchor")];
    char CSSPropStringPool_str536[sizeof("-webkit-perspective")];
    char CSSPropStringPool_str537[sizeof("-webkit-animation-delay")];
    char CSSPropStringPool_str538[sizeof("-webkit-logical-width")];
    char CSSPropStringPool_str539[sizeof("justify-content")];
    char CSSPropStringPool_str540[sizeof("-internal-visited-column-rule-color")];
    char CSSPropStringPool_str541[sizeof("-webkit-font-feature-settings")];
    char CSSPropStringPool_str542[sizeof("-epub-text-transform")];
    char CSSPropStringPool_str543[sizeof("contain-intrinsic-height")];
    char CSSPropStringPool_str544[sizeof("overflow-wrap")];
    char CSSPropStringPool_str545[sizeof("text-emphasis")];
    char CSSPropStringPool_str546[sizeof("border-block-start-width")];
    char CSSPropStringPool_str547[sizeof("-webkit-font-smoothing")];
    char CSSPropStringPool_str548[sizeof("-internal-visited-border-inline-end-color")];
    char CSSPropStringPool_str549[sizeof("-internal-visited-border-bottom-color")];
    char CSSPropStringPool_str550[sizeof("border-right-width")];
    char CSSPropStringPool_str551[sizeof("-webkit-perspective-origin")];
    char CSSPropStringPool_str552[sizeof("text-emphasis-color")];
    char CSSPropStringPool_str553[sizeof("list-style-type")];
    char CSSPropStringPool_str554[sizeof("text-decoration-skip-ink")];
    char CSSPropStringPool_str555[sizeof("-epub-text-combine")];
    char CSSPropStringPool_str556[sizeof("text-emphasis-position")];
    char CSSPropStringPool_str557[sizeof("-webkit-border-start-style")];
    char CSSPropStringPool_str558[sizeof("suffix")];
    char CSSPropStringPool_str559[sizeof("visibility")];
    char CSSPropStringPool_str560[sizeof("-webkit-ruby-position")];
    char CSSPropStringPool_str561[sizeof("overflow-block")];
    char CSSPropStringPool_str562[sizeof("-webkit-border-bottom-right-radius")];
    char CSSPropStringPool_str563[sizeof("-webkit-box-pack")];
    char CSSPropStringPool_str564[sizeof("font-synthesis")];
    char CSSPropStringPool_str565[sizeof("background-position-x")];
    char CSSPropStringPool_str566[sizeof("-webkit-text-combine")];
    char CSSPropStringPool_str567[sizeof("-webkit-column-rule-style")];
    char CSSPropStringPool_str568[sizeof("-webkit-border-start-width")];
    char CSSPropStringPool_str569[sizeof("syntax")];
    char CSSPropStringPool_str570[sizeof("-webkit-mask-position-y")];
    char CSSPropStringPool_str571[sizeof("position-visibility")];
    char CSSPropStringPool_str572[sizeof("overscroll-behavior")];
    char CSSPropStringPool_str573[sizeof("-internal-overflow-block")];
    char CSSPropStringPool_str574[sizeof("-webkit-border-top-right-radius")];
    char CSSPropStringPool_str575[sizeof("-internal-visited-border-left-color")];
    char CSSPropStringPool_str576[sizeof("-webkit-transform-style")];
    char CSSPropStringPool_str577[sizeof("overscroll-behavior-inline")];
    char CSSPropStringPool_str578[sizeof("max-block-size")];
    char CSSPropStringPool_str579[sizeof("flex-flow")];
    char CSSPropStringPool_str580[sizeof("-webkit-column-rule-width")];
    char CSSPropStringPool_str581[sizeof("font-synthesis-small-caps")];
    char CSSPropStringPool_str582[sizeof("-webkit-transition-property")];
    char CSSPropStringPool_str583[sizeof("-webkit-transform-origin-y")];
    char CSSPropStringPool_str584[sizeof("max-width")];
    char CSSPropStringPool_str585[sizeof("additive-symbols")];
    char CSSPropStringPool_str586[sizeof("text-underline-offset")];
    char CSSPropStringPool_str587[sizeof("justify-self")];
    char CSSPropStringPool_str588[sizeof("text-shadow")];
    char CSSPropStringPool_str589[sizeof("-webkit-transition-delay")];
    char CSSPropStringPool_str590[sizeof("-webkit-mask-box-image")];
    char CSSPropStringPool_str591[sizeof("-webkit-logical-height")];
    char CSSPropStringPool_str592[sizeof("text-combine-upright")];
    char CSSPropStringPool_str593[sizeof("stroke-dashoffset")];
    char CSSPropStringPool_str594[sizeof("-internal-visited-background-color")];
    char CSSPropStringPool_str595[sizeof("font-variant-ligatures")];
    char CSSPropStringPool_str596[sizeof("-webkit-flex")];
    char CSSPropStringPool_str597[sizeof("view-timeline-axis")];
    char CSSPropStringPool_str598[sizeof("-webkit-text-stroke")];
    char CSSPropStringPool_str599[sizeof("-webkit-user-modify")];
    char CSSPropStringPool_str600[sizeof("-webkit-background-clip")];
    char CSSPropStringPool_str601[sizeof("-internal-visited-border-block-start-color")];
    char CSSPropStringPool_str602[sizeof("-webkit-min-logical-width")];
    char CSSPropStringPool_str603[sizeof("-webkit-tap-highlight-color")];
    char CSSPropStringPool_str604[sizeof("-webkit-mask-box-image-slice")];
    char CSSPropStringPool_str605[sizeof("text-decoration-style")];
    char CSSPropStringPool_str606[sizeof("-webkit-mask-box-image-repeat")];
    char CSSPropStringPool_str607[sizeof("-webkit-background-origin")];
    char CSSPropStringPool_str608[sizeof("-webkit-mask-position-x")];
    char CSSPropStringPool_str609[sizeof("-webkit-text-stroke-color")];
    char CSSPropStringPool_str610[sizeof("overflow-anchor")];
    char CSSPropStringPool_str611[sizeof("-internal-visited-border-block-end-color")];
    char CSSPropStringPool_str612[sizeof("hyphens")];
    char CSSPropStringPool_str613[sizeof("box-shadow")];
    char CSSPropStringPool_str614[sizeof("-epub-text-emphasis")];
    char CSSPropStringPool_str615[sizeof("-webkit-text-fill-color")];
    char CSSPropStringPool_str616[sizeof("-webkit-text-orientation")];
    char CSSPropStringPool_str617[sizeof("text-decoration-thickness")];
    char CSSPropStringPool_str618[sizeof("-webkit-border-before-style")];
    char CSSPropStringPool_str619[sizeof("-epub-text-emphasis-color")];
    char CSSPropStringPool_str620[sizeof("-webkit-transform-origin-x")];
    char CSSPropStringPool_str621[sizeof("-webkit-flex-direction")];
    char CSSPropStringPool_str622[sizeof("-webkit-border-end-style")];
    char CSSPropStringPool_str623[sizeof("-webkit-box-orient")];
    char CSSPropStringPool_str624[sizeof("-webkit-flex-basis")];
    char CSSPropStringPool_str625[sizeof("-webkit-mask-box-image-source")];
    char CSSPropStringPool_str626[sizeof("-webkit-mask-box-image-outset")];
    char CSSPropStringPool_str627[sizeof("-webkit-box-reflect")];
    char CSSPropStringPool_str628[sizeof("content-visibility")];
    char CSSPropStringPool_str629[sizeof("-webkit-border-before-width")];
    char CSSPropStringPool_str630[sizeof("text-size-adjust")];
    char CSSPropStringPool_str631[sizeof("text-box-trim")];
    char CSSPropStringPool_str632[sizeof("-webkit-border-after-style")];
    char CSSPropStringPool_str633[sizeof("-internal-visited-border-right-color")];
    char CSSPropStringPool_str634[sizeof("-webkit-border-end-width")];
    char CSSPropStringPool_str635[sizeof("-webkit-shape-outside")];
    char CSSPropStringPool_str636[sizeof("-webkit-box-direction")];
    char CSSPropStringPool_str637[sizeof("-internal-empty-line-height")];
    char CSSPropStringPool_str638[sizeof("flex-shrink")];
    char CSSPropStringPool_str639[sizeof("overflow-y")];
    char CSSPropStringPool_str640[sizeof("text-box-edge")];
    char CSSPropStringPool_str641[sizeof("-webkit-border-after-width")];
    char CSSPropStringPool_str642[sizeof("max-height")];
    char CSSPropStringPool_str643[sizeof("-webkit-text-size-adjust")];
    char CSSPropStringPool_str644[sizeof("overscroll-behavior-block")];
    char CSSPropStringPool_str645[sizeof("-webkit-flex-grow")];
    char CSSPropStringPool_str646[sizeof("-webkit-min-logical-height")];
    char CSSPropStringPool_str647[sizeof("-webkit-box-decoration-break")];
    char CSSPropStringPool_str648[sizeof("shape-image-threshold")];
    char CSSPropStringPool_str649[sizeof("-webkit-justify-content")];
    char CSSPropStringPool_str650[sizeof("-webkit-text-emphasis")];
    char CSSPropStringPool_str651[sizeof("-webkit-flex-wrap")];
    char CSSPropStringPool_str652[sizeof("-webkit-box-ordinal-group")];
    char CSSPropStringPool_str653[sizeof("-webkit-text-emphasis-color")];
    char CSSPropStringPool_str654[sizeof("-webkit-box-align")];
    char CSSPropStringPool_str655[sizeof("overflow-x")];
    char CSSPropStringPool_str656[sizeof("-webkit-text-emphasis-position")];
    char CSSPropStringPool_str657[sizeof("text-overflow")];
    char CSSPropStringPool_str658[sizeof("-webkit-background-size")];
    char CSSPropStringPool_str659[sizeof("popover-hide-delay")];
    char CSSPropStringPool_str660[sizeof("-webkit-column-width")];
    char CSSPropStringPool_str661[sizeof("-internal-visited-text-decoration-color")];
    char CSSPropStringPool_str662[sizeof("backface-visibility")];
    char CSSPropStringPool_str663[sizeof("-webkit-perspective-origin-y")];
    char CSSPropStringPool_str664[sizeof("-webkit-flex-flow")];
    char CSSPropStringPool_str665[sizeof("-webkit-text-decorations-in-effect")];
    char CSSPropStringPool_str666[sizeof("-webkit-box-sizing")];
    char CSSPropStringPool_str667[sizeof("object-view-box")];
    char CSSPropStringPool_str668[sizeof("popover-show-delay")];
    char CSSPropStringPool_str669[sizeof("-webkit-box-shadow")];
    char CSSPropStringPool_str670[sizeof("-webkit-shape-image-threshold")];
    char CSSPropStringPool_str671[sizeof("text-emphasis-style")];
    char CSSPropStringPool_str672[sizeof("-internal-visited-text-stroke-color")];
    char CSSPropStringPool_str673[sizeof("font-synthesis-style")];
    char CSSPropStringPool_str674[sizeof("overscroll-behavior-y")];
    char CSSPropStringPool_str675[sizeof("-internal-visited-text-fill-color")];
    char CSSPropStringPool_str676[sizeof("-webkit-perspective-origin-x")];
    char CSSPropStringPool_str677[sizeof("-webkit-text-security")];
    char CSSPropStringPool_str678[sizeof("hyphenate-limit-chars")];
    char CSSPropStringPool_str679[sizeof("hyphenate-character")];
    char CSSPropStringPool_str680[sizeof("overscroll-behavior-x")];
    char CSSPropStringPool_str681[sizeof("-webkit-flex-shrink")];
    char CSSPropStringPool_str682[sizeof("-webkit-mask-box-image-width")];
    char CSSPropStringPool_str683[sizeof("-epub-text-emphasis-style")];
    char CSSPropStringPool_str684[sizeof("-webkit-text-stroke-width")];
    char CSSPropStringPool_str685[sizeof("-webkit-box-flex")];
    char CSSPropStringPool_str686[sizeof("-webkit-max-logical-width")];
    char CSSPropStringPool_str687[sizeof("-internal-visited-text-emphasis-color")];
    char CSSPropStringPool_str688[sizeof("-webkit-text-emphasis-style")];
    char CSSPropStringPool_str689[sizeof("-webkit-max-logical-height")];
    char CSSPropStringPool_str690[sizeof("font-synthesis-weight")];
    char CSSPropStringPool_str691[sizeof("-webkit-backface-visibility")];
    char CSSPropStringPool_str692[sizeof("-webkit-hyphenate-character")];
  };
static const struct CSSPropStringPool_t CSSPropStringPool_contents =
  {
    "r",
    "all",
    "src",
    "top",
    "clip",
    "color",
    "clear",
    "inset",
    "scale",
    "gap",
    "rotate",
    "page",
    "content",
    "range",
    "contain",
    "position",
    "d",
    "margin",
    "container",
    "isolation",
    "translate",
    "animation",
    "transition",
    "inset-area",
    "stop-color",
    "caret-color",
    "line-clamp",
    "pad",
    "appearance",
    "inset-inline",
    "accent-color",
    "scroll-start",
    "order",
    "place-items",
    "aspect-ratio",
    "app-region",
    "margin-top",
    "grid",
    "place-content",
    "initial-letter",
    "align-items",
    "container-name",
    "align-content",
    "timeline-scope",
    "scroll-timeline",
    "margin-inline",
    "inset-inline-end",
    "scroll-margin",
    "animation-name",
    "direction",
    "letter-spacing",
    "scroll-snap-stop",
    "animation-range",
    "page-orientation",
    "grid-area",
    "paint-order",
    "bottom",
    "scroll-start-inline",
    "grid-gap",
    "image-orientation",
    "caption-side",
    "scroll-margin-top",
    "cursor",
    "quotes",
    "scroll-timeline-name",
    "scroll-start-target",
    "outline",
    "scroll-margin-inline",
    "columns",
    "animation-composition",
    "grid-template",
    "color-rendering",
    "clip-rule",
    "animation-range-start",
    "padding",
    "base-palette",
    "counter-set",
    "user-select",
    "border",
    "margin-inline-end",
    "image-rendering",
    "outline-color",
    "scrollbar-color",
    "column-span",
    "counter-reset",
    "scroll-start-target-inline",
    "column-gap",
    "scroll-margin-inline-start",
    "margin-bottom",
    "padding-top",
    "animation-range-end",
    "grid-template-areas",
    "border-top",
    "inset-inline-start",
    "padding-inline",
    "border-color",
    "scroll-padding",
    "counter-increment",
    "border-inline",
    "scroll-margin-inline-end",
    "color-interpolation",
    "border-image",
    "border-collapse",
    "grid-column",
    "border-top-color",
    "scroll-margin-bottom",
    "margin-inline-start",
    "border-spacing",
    "unicode-range",
    "mask",
    "padding-inline-start",
    "dominant-baseline",
    "speak",
    "stroke",
    "border-inline-color",
    "marker",
    "scroll-padding-bottom",
    "row-gap",
    "column-rule",
    "speak-as",
    "border-image-repeat",
    "animation-direction",
    "column-count",
    "grid-column-start",
    "grid-column-gap",
    "baseline-source",
    "mask-clip",
    "fill",
    "left",
    "font",
    "border-inline-end",
    "float",
    "mask-repeat",
    "filter",
    "padding-bottom",
    "marker-start",
    "scrollbar-gutter",
    "mask-origin",
    "mask-image",
    "border-bottom",
    "stroke-linecap",
    "mask-position",
    "grid-column-end",
    "column-rule-color",
    "grid-row",
    "grid-template-columns",
    "mask-composite",
    "transform",
    "border-inline-end-color",
    "place-self",
    "marker-end",
    "mask-mode",
    "scroll-padding-top",
    "marker-mid",
    "align-self",
    "reading-order-items",
    "font-palette",
    "writing-mode",
    "border-bottom-color",
    "word-spacing",
    "margin-left",
    "border-inline-start",
    "grid-row-start",
    "grid-row-gap",
    "unicode-bidi",
    "line-break",
    "animation-iteration-count",
    "border-radius",
    "inset-block",
    "transition-duration",
    "grid-auto-columns",
    "flood-color",
    "transform-origin",
    "animation-timeline",
    "grid-row-end",
    "border-inline-start-color",
    "margin-block",
    "grid-template-rows",
    "-internal-forced-color",
    "font-optical-sizing",
    "size",
    "inset-block-start",
    "zoom",
    "fill-rule",
    "margin-block-end",
    "scroll-start-block",
    "resize",
    "page-break-inside",
    "break-inside",
    "column-fill",
    "border-start-end-radius",
    "animation-fill-mode",
    "object-position",
    "scroll-margin-block",
    "inset-block-end",
    "inline-size",
    "grid-auto-rows",
    "padding-left",
    "scroll-padding-block",
    "border-left",
    "border-image-outset",
    "scroll-start-target-block",
    "alignment-baseline",
    "min-inline-size",
    "scroll-margin-block-start",
    "border-top-width",
    "widows",
    "-internal-align-content-block",
    "padding-block",
    "scroll-padding-block-start",
    "border-left-color",
    "scroll-padding-left",
    "border-block",
    "scroll-padding-inline",
    "word-wrap",
    "margin-block-start",
    "scroll-margin-block-end",
    "tab-size",
    "border-end-end-radius",
    "border-image-slice",
    "background",
    "animation-duration",
    "border-block-end",
    "border-start-start-radius",
    "right",
    "color-interpolation-filters",
    "scroll-padding-block-end",
    "transition-delay",
    "orphans",
    "inherits",
    "padding-inline-end",
    "transition-timing-function",
    "-internal-forced-outline-color",
    "border-block-color",
    "scroll-padding-inline-start",
    "clip-path",
    "offset",
    "background-clip",
    "font-kerning",
    "background-color",
    "stroke-linejoin",
    "anchor-name",
    "border-block-end-color",
    "color-scheme",
    "-epub-writing-mode",
    "padding-block-end",
    "-internal-forced-border-color",
    "border-end-start-radius",
    "scroll-padding-inline-end",
    "-webkit-locale",
    "stroke-miterlimit",
    "position-anchor",
    "shape-margin",
    "margin-right",
    "lighting-color",
    "offset-rotate",
    "scroll-snap-align",
    "fallback",
    "word-break",
    "-epub-caption-side",
    "-webkit-animation",
    "offset-position",
    "-epub-word-break",
    "-webkit-appearance",
    "break-after",
    "-webkit-order",
    "border-block-start",
    "padding-block-start",
    "-webkit-margin-start",
    "-webkit-align-content",
    "shape-rendering",
    "scroll-margin-left",
    "background-repeat",
    "-webkit-animation-name",
    "mask-size",
    "object-fit",
    "touch-action",
    "border-block-start-color",
    "offset-distance",
    "page-break-after",
    "y",
    "background-position",
    "border-top-left-radius",
    "ry",
    "cy",
    "-webkit-columns",
    "font-size",
    "padding-right",
    "-webkit-line-clamp",
    "backdrop-filter",
    "outline-offset",
    "border-right",
    "grid-auto-flow",
    "types",
    "break-before",
    "negative",
    "system",
    "-webkit-user-select",
    "opacity",
    "shape-outside",
    "-webkit-column-span",
    "-webkit-animation-direction",
    "background-blend-mode",
    "perspective",
    "navigation",
    "-webkit-align-items",
    "list-style",
    "block-size",
    "scroll-padding-right",
    "pointer-events",
    "border-right-color",
    "-webkit-padding-start",
    "empty-cells",
    "position-try",
    "-webkit-border-start",
    "vertical-align",
    "stop-opacity",
    "white-space",
    "page-break-before",
    "scroll-start-y",
    "container-type",
    "width",
    "will-change",
    "min-block-size",
    "display",
    "line-gap-override",
    "field-sizing",
    "contain-intrinsic-size",
    "-webkit-padding-end",
    "position-try-order",
    "forced-color-adjust",
    "-webkit-mask",
    "-webkit-border-start-color",
    "override-colors",
    "min-width",
    "ascent-override",
    "symbols",
    "animation-play-state",
    "-webkit-animation-iteration-count",
    "-webkit-column-rule",
    "-webkit-animation-duration",
    "font-stretch",
    "contain-intrinsic-inline-size",
    "x",
    "-webkit-mask-size",
    "scroll-start-target-y",
    "animation-delay",
    "rx",
    "cx",
    "white-space-collapse",
    "background-origin",
    "initial-value",
    "-webkit-mask-repeat",
    "background-image",
    "-webkit-filter",
    "-webkit-user-drag",
    "-internal-font-size-delta",
    "-webkit-mask-position",
    "outline-style",
    "-webkit-column-rule-color",
    "size-adjust",
    "origin-trial-test-property",
    "-webkit-mask-composite",
    "font-feature-settings",
    "-webkit-transition",
    "-webkit-transform",
    "-webkit-column-count",
    "border-style",
    "-webkit-app-region",
    "-webkit-writing-mode",
    "transition-behavior",
    "scroll-margin-right",
    "text-align",
    "list-style-position",
    "-webkit-margin-before",
    "position-try-options",
    "scroll-start-x",
    "-webkit-border-radius",
    "text-spacing",
    "column-width",
    "outline-width",
    "scrollbar-width",
    "text-orientation",
    "baseline-shift",
    "border-block-style",
    "-internal-forced-background-color",
    "border-image-source",
    "-webkit-transform-origin",
    "text-align-last",
    "-webkit-mask-image",
    "table-layout",
    "background-size",
    "-webkit-margin-end",
    "text-indent",
    "view-timeline",
    "ruby-position",
    "-webkit-rtl-ordering",
    "border-width",
    "border-image-width",
    "scroll-timeline-axis",
    "mask-type",
    "text-decoration",
    "text-rendering",
    "scroll-start-target-x",
    "view-timeline-inset",
    "stroke-opacity",
    "-webkit-margin-after",
    "column-rule-style",
    "view-transition-name",
    "-webkit-animation-fill-mode",
    "font-variant",
    "view-transition-class",
    "text-decoration-line",
    "font-style",
    "border-inline-end-style",
    "text-decoration-color",
    "-webkit-padding-after",
    "fill-opacity",
    "text-autospace",
    "animation-timing-function",
    "-webkit-border-before",
    "height",
    "border-inline-style",
    "stroke-width",
    "font-variant-caps",
    "font-weight",
    "-webkit-border-image",
    "border-top-right-radius",
    "-webkit-column-break-inside",
    "transform-style",
    "scroll-snap-type",
    "-webkit-border-vertical-spacing",
    "border-bottom-left-radius",
    "-webkit-border-end",
    "font-variant-position",
    "min-height",
    "line-height",
    "list-style-image",
    "-webkit-border-before-color",
    "view-timeline-name",
    "transition-property",
    "border-inline-end-width",
    "font-display",
    "border-inline-start-style",
    "font-variant-east-asian",
    "flood-opacity",
    "-webkit-print-color-adjust",
    "-webkit-transition-duration",
    "font-variation-settings",
    "offset-path",
    "text-wrap",
    "-webkit-mask-clip",
    "-webkit-border-end-color",
    "offset-anchor",
    "-webkit-animation-timing-function",
    "-webkit-padding-before",
    "-webkit-border-after",
    "math-depth",
    "mix-blend-mode",
    "flex",
    "-webkit-mask-origin",
    "-internal-visited-color",
    "text-underline-position",
    "contain-intrinsic-block-size",
    "border-bottom-style",
    "descent-override",
    "prefix",
    "dynamic-range-limit",
    "border-right-style",
    "border-inline-start-width",
    "-webkit-border-after-color",
    "background-attachment",
    "font-variant-numeric",
    "contain-intrinsic-width",
    "-webkit-align-self",
    "column-rule-width",
    "-epub-text-orientation",
    "-internal-visited-caret-color",
    "-internal-forced-visited-color",
    "overflow",
    "font-variant-alternates",
    "-webkit-line-break",
    "border-top-style",
    "text-transform",
    "border-left-style",
    "buffered-rendering",
    "-webkit-clip-path",
    "-webkit-column-gap",
    "overflow-inline",
    "text-spacing-trim",
    "flex-direction",
    "-webkit-shape-margin",
    "flex-basis",
    "border-block-width",
    "math-style",
    "-webkit-column-break-after",
    "stroke-dasharray",
    "-webkit-border-horizontal-spacing",
    "border-block-end-style",
    "border-inline-width",
    "font-size-adjust",
    "transform-box",
    "-webkit-border-bottom-left-radius",
    "vector-effect",
    "-internal-visited-outline-color",
    "perspective-origin",
    "font-family",
    "-webkit-opacity",
    "-internal-overflow-inline",
    "border-bottom-right-radius",
    "z-index",
    "font-variant-emoji",
    "max-inline-size",
    "math-shift",
    "border-block-end-width",
    "-internal-visited-border-top-color",
    "scroll-behavior",
    "-webkit-transform-origin-z",
    "-webkit-column-break-before",
    "-webkit-border-top-left-radius",
    "-internal-visited-stroke",
    "border-left-width",
    "border-bottom-width",
    "flex-wrap",
    "border-block-start-style",
    "overflow-clip-margin",
    "-webkit-transition-timing-function",
    "flex-grow",
    "box-sizing",
    "-internal-visited-fill",
    "-webkit-animation-play-state",
    "background-position-y",
    "-internal-visited-border-inline-start-color",
    "overlay",
    "justify-items",
    "text-anchor",
    "-webkit-perspective",
    "-webkit-animation-delay",
    "-webkit-logical-width",
    "justify-content",
    "-internal-visited-column-rule-color",
    "-webkit-font-feature-settings",
    "-epub-text-transform",
    "contain-intrinsic-height",
    "overflow-wrap",
    "text-emphasis",
    "border-block-start-width",
    "-webkit-font-smoothing",
    "-internal-visited-border-inline-end-color",
    "-internal-visited-border-bottom-color",
    "border-right-width",
    "-webkit-perspective-origin",
    "text-emphasis-color",
    "list-style-type",
    "text-decoration-skip-ink",
    "-epub-text-combine",
    "text-emphasis-position",
    "-webkit-border-start-style",
    "suffix",
    "visibility",
    "-webkit-ruby-position",
    "overflow-block",
    "-webkit-border-bottom-right-radius",
    "-webkit-box-pack",
    "font-synthesis",
    "background-position-x",
    "-webkit-text-combine",
    "-webkit-column-rule-style",
    "-webkit-border-start-width",
    "syntax",
    "-webkit-mask-position-y",
    "position-visibility",
    "overscroll-behavior",
    "-internal-overflow-block",
    "-webkit-border-top-right-radius",
    "-internal-visited-border-left-color",
    "-webkit-transform-style",
    "overscroll-behavior-inline",
    "max-block-size",
    "flex-flow",
    "-webkit-column-rule-width",
    "font-synthesis-small-caps",
    "-webkit-transition-property",
    "-webkit-transform-origin-y",
    "max-width",
    "additive-symbols",
    "text-underline-offset",
    "justify-self",
    "text-shadow",
    "-webkit-transition-delay",
    "-webkit-mask-box-image",
    "-webkit-logical-height",
    "text-combine-upright",
    "stroke-dashoffset",
    "-internal-visited-background-color",
    "font-variant-ligatures",
    "-webkit-flex",
    "view-timeline-axis",
    "-webkit-text-stroke",
    "-webkit-user-modify",
    "-webkit-background-clip",
    "-internal-visited-border-block-start-color",
    "-webkit-min-logical-width",
    "-webkit-tap-highlight-color",
    "-webkit-mask-box-image-slice",
    "text-decoration-style",
    "-webkit-mask-box-image-repeat",
    "-webkit-background-origin",
    "-webkit-mask-position-x",
    "-webkit-text-stroke-color",
    "overflow-anchor",
    "-internal-visited-border-block-end-color",
    "hyphens",
    "box-shadow",
    "-epub-text-emphasis",
    "-webkit-text-fill-color",
    "-webkit-text-orientation",
    "text-decoration-thickness",
    "-webkit-border-before-style",
    "-epub-text-emphasis-color",
    "-webkit-transform-origin-x",
    "-webkit-flex-direction",
    "-webkit-border-end-style",
    "-webkit-box-orient",
    "-webkit-flex-basis",
    "-webkit-mask-box-image-source",
    "-webkit-mask-box-image-outset",
    "-webkit-box-reflect",
    "content-visibility",
    "-webkit-border-before-width",
    "text-size-adjust",
    "text-box-trim",
    "-webkit-border-after-style",
    "-internal-visited-border-right-color",
    "-webkit-border-end-width",
    "-webkit-shape-outside",
    "-webkit-box-direction",
    "-internal-empty-line-height",
    "flex-shrink",
    "overflow-y",
    "text-box-edge",
    "-webkit-border-after-width",
    "max-height",
    "-webkit-text-size-adjust",
    "overscroll-behavior-block",
    "-webkit-flex-grow",
    "-webkit-min-logical-height",
    "-webkit-box-decoration-break",
    "shape-image-threshold",
    "-webkit-justify-content",
    "-webkit-text-emphasis",
    "-webkit-flex-wrap",
    "-webkit-box-ordinal-group",
    "-webkit-text-emphasis-color",
    "-webkit-box-align",
    "overflow-x",
    "-webkit-text-emphasis-position",
    "text-overflow",
    "-webkit-background-size",
    "popover-hide-delay",
    "-webkit-column-width",
    "-internal-visited-text-decoration-color",
    "backface-visibility",
    "-webkit-perspective-origin-y",
    "-webkit-flex-flow",
    "-webkit-text-decorations-in-effect",
    "-webkit-box-sizing",
    "object-view-box",
    "popover-show-delay",
    "-webkit-box-shadow",
    "-webkit-shape-image-threshold",
    "text-emphasis-style",
    "-internal-visited-text-stroke-color",
    "font-synthesis-style",
    "overscroll-behavior-y",
    "-internal-visited-text-fill-color",
    "-webkit-perspective-origin-x",
    "-webkit-text-security",
    "hyphenate-limit-chars",
    "hyphenate-character",
    "overscroll-behavior-x",
    "-webkit-flex-shrink",
    "-webkit-mask-box-image-width",
    "-epub-text-emphasis-style",
    "-webkit-text-stroke-width",
    "-webkit-box-flex",
    "-webkit-max-logical-width",
    "-internal-visited-text-emphasis-color",
    "-webkit-text-emphasis-style",
    "-webkit-max-logical-height",
    "font-synthesis-weight",
    "-webkit-backface-visibility",
    "-webkit-hyphenate-character"
  };
#define CSSPropStringPool ((const char *) &CSSPropStringPool_contents)
const struct Property *
CSSPropertyNamesHash::findPropertyImpl (const char *str, unsigned int len)
{
  enum
    {
      TOTAL_KEYWORDS = 693,
      MIN_WORD_LENGTH = 1,
      MAX_WORD_LENGTH = 43,
      MIN_HASH_VALUE = 10,
      MAX_HASH_VALUE = 4783
    };

  static const struct Property property_word_list[] =
    {
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str0), static_cast<int>(CSSPropertyID::kR)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str1), static_cast<int>(CSSPropertyID::kAll)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str2), static_cast<int>(CSSPropertyID::kSrc)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str3), static_cast<int>(CSSPropertyID::kTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str4), static_cast<int>(CSSPropertyID::kClip)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str5), static_cast<int>(CSSPropertyID::kColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str6), static_cast<int>(CSSPropertyID::kClear)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str7), static_cast<int>(CSSPropertyID::kInset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str8), static_cast<int>(CSSPropertyID::kScale)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str9), static_cast<int>(CSSPropertyID::kGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str10), static_cast<int>(CSSPropertyID::kRotate)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str11), static_cast<int>(CSSPropertyID::kPage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str12), static_cast<int>(CSSPropertyID::kContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str13), static_cast<int>(CSSPropertyID::kRange)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str14), static_cast<int>(CSSPropertyID::kContain)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str15), static_cast<int>(CSSPropertyID::kPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str16), static_cast<int>(CSSPropertyID::kD)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str17), static_cast<int>(CSSPropertyID::kMargin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str18), static_cast<int>(CSSPropertyID::kContainer)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str19), static_cast<int>(CSSPropertyID::kIsolation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str20), static_cast<int>(CSSPropertyID::kTranslate)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str21), static_cast<int>(CSSPropertyID::kAnimation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str22), static_cast<int>(CSSPropertyID::kTransition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str23), static_cast<int>(CSSPropertyID::kInsetArea)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str24), static_cast<int>(CSSPropertyID::kStopColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str25), static_cast<int>(CSSPropertyID::kCaretColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str26), static_cast<int>(CSSPropertyID::kLineClamp)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str27), static_cast<int>(CSSPropertyID::kPad)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str28), static_cast<int>(CSSPropertyID::kAppearance)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str29), static_cast<int>(CSSPropertyID::kInsetInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str30), static_cast<int>(CSSPropertyID::kAccentColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str31), static_cast<int>(CSSPropertyID::kScrollStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str32), static_cast<int>(CSSPropertyID::kOrder)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str33), static_cast<int>(CSSPropertyID::kPlaceItems)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str34), static_cast<int>(CSSPropertyID::kAspectRatio)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str35), static_cast<int>(CSSPropertyID::kAppRegion)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str36), static_cast<int>(CSSPropertyID::kMarginTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str37), static_cast<int>(CSSPropertyID::kGrid)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str38), static_cast<int>(CSSPropertyID::kPlaceContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str39), static_cast<int>(CSSPropertyID::kInitialLetter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str40), static_cast<int>(CSSPropertyID::kAlignItems)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str41), static_cast<int>(CSSPropertyID::kContainerName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str42), static_cast<int>(CSSPropertyID::kAlignContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str43), static_cast<int>(CSSPropertyID::kTimelineScope)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str44), static_cast<int>(CSSPropertyID::kScrollTimeline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str45), static_cast<int>(CSSPropertyID::kMarginInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str46), static_cast<int>(CSSPropertyID::kInsetInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str47), static_cast<int>(CSSPropertyID::kScrollMargin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str48), static_cast<int>(CSSPropertyID::kAnimationName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str49), static_cast<int>(CSSPropertyID::kDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str50), static_cast<int>(CSSPropertyID::kLetterSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str51), static_cast<int>(CSSPropertyID::kScrollSnapStop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str52), static_cast<int>(CSSPropertyID::kAnimationRange)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str53), static_cast<int>(CSSPropertyID::kPageOrientation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str54), static_cast<int>(CSSPropertyID::kGridArea)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str55), static_cast<int>(CSSPropertyID::kPaintOrder)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str56), static_cast<int>(CSSPropertyID::kBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str57), static_cast<int>(CSSPropertyID::kScrollStartInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str58), static_cast<int>(CSSPropertyID::kAliasGridGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str59), static_cast<int>(CSSPropertyID::kImageOrientation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str60), static_cast<int>(CSSPropertyID::kCaptionSide)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str61), static_cast<int>(CSSPropertyID::kScrollMarginTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str62), static_cast<int>(CSSPropertyID::kCursor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str63), static_cast<int>(CSSPropertyID::kQuotes)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str64), static_cast<int>(CSSPropertyID::kScrollTimelineName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str65), static_cast<int>(CSSPropertyID::kScrollStartTarget)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str66), static_cast<int>(CSSPropertyID::kOutline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str67), static_cast<int>(CSSPropertyID::kScrollMarginInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str68), static_cast<int>(CSSPropertyID::kColumns)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str69), static_cast<int>(CSSPropertyID::kAnimationComposition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str70), static_cast<int>(CSSPropertyID::kGridTemplate)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str71), static_cast<int>(CSSPropertyID::kColorRendering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str72), static_cast<int>(CSSPropertyID::kClipRule)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str73), static_cast<int>(CSSPropertyID::kAnimationRangeStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str74), static_cast<int>(CSSPropertyID::kPadding)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str75), static_cast<int>(CSSPropertyID::kBasePalette)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str76), static_cast<int>(CSSPropertyID::kCounterSet)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str77), static_cast<int>(CSSPropertyID::kUserSelect)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str78), static_cast<int>(CSSPropertyID::kBorder)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str79), static_cast<int>(CSSPropertyID::kMarginInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str80), static_cast<int>(CSSPropertyID::kImageRendering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str81), static_cast<int>(CSSPropertyID::kOutlineColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str82), static_cast<int>(CSSPropertyID::kScrollbarColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str83), static_cast<int>(CSSPropertyID::kColumnSpan)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str84), static_cast<int>(CSSPropertyID::kCounterReset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str85), static_cast<int>(CSSPropertyID::kScrollStartTargetInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str86), static_cast<int>(CSSPropertyID::kColumnGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str87), static_cast<int>(CSSPropertyID::kScrollMarginInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str88), static_cast<int>(CSSPropertyID::kMarginBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str89), static_cast<int>(CSSPropertyID::kPaddingTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str90), static_cast<int>(CSSPropertyID::kAnimationRangeEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str91), static_cast<int>(CSSPropertyID::kGridTemplateAreas)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str92), static_cast<int>(CSSPropertyID::kBorderTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str93), static_cast<int>(CSSPropertyID::kInsetInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str94), static_cast<int>(CSSPropertyID::kPaddingInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str95), static_cast<int>(CSSPropertyID::kBorderColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str96), static_cast<int>(CSSPropertyID::kScrollPadding)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str97), static_cast<int>(CSSPropertyID::kCounterIncrement)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str98), static_cast<int>(CSSPropertyID::kBorderInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str99), static_cast<int>(CSSPropertyID::kScrollMarginInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str100), static_cast<int>(CSSPropertyID::kColorInterpolation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str101), static_cast<int>(CSSPropertyID::kBorderImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str102), static_cast<int>(CSSPropertyID::kBorderCollapse)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str103), static_cast<int>(CSSPropertyID::kGridColumn)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str104), static_cast<int>(CSSPropertyID::kBorderTopColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str105), static_cast<int>(CSSPropertyID::kScrollMarginBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str106), static_cast<int>(CSSPropertyID::kMarginInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str107), static_cast<int>(CSSPropertyID::kBorderSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str108), static_cast<int>(CSSPropertyID::kUnicodeRange)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str109), static_cast<int>(CSSPropertyID::kMask)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str110), static_cast<int>(CSSPropertyID::kPaddingInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str111), static_cast<int>(CSSPropertyID::kDominantBaseline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str112), static_cast<int>(CSSPropertyID::kSpeak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str113), static_cast<int>(CSSPropertyID::kStroke)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str114), static_cast<int>(CSSPropertyID::kBorderInlineColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str115), static_cast<int>(CSSPropertyID::kMarker)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str116), static_cast<int>(CSSPropertyID::kScrollPaddingBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str117), static_cast<int>(CSSPropertyID::kRowGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str118), static_cast<int>(CSSPropertyID::kColumnRule)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str119), static_cast<int>(CSSPropertyID::kSpeakAs)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str120), static_cast<int>(CSSPropertyID::kBorderImageRepeat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str121), static_cast<int>(CSSPropertyID::kAnimationDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str122), static_cast<int>(CSSPropertyID::kColumnCount)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str123), static_cast<int>(CSSPropertyID::kGridColumnStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str124), static_cast<int>(CSSPropertyID::kAliasGridColumnGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str125), static_cast<int>(CSSPropertyID::kBaselineSource)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str126), static_cast<int>(CSSPropertyID::kMaskClip)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str127), static_cast<int>(CSSPropertyID::kFill)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str128), static_cast<int>(CSSPropertyID::kLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str129), static_cast<int>(CSSPropertyID::kFont)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str130), static_cast<int>(CSSPropertyID::kBorderInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str131), static_cast<int>(CSSPropertyID::kFloat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str132), static_cast<int>(CSSPropertyID::kMaskRepeat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str133), static_cast<int>(CSSPropertyID::kFilter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str134), static_cast<int>(CSSPropertyID::kPaddingBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str135), static_cast<int>(CSSPropertyID::kMarkerStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str136), static_cast<int>(CSSPropertyID::kScrollbarGutter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str137), static_cast<int>(CSSPropertyID::kMaskOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str138), static_cast<int>(CSSPropertyID::kMaskImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str139), static_cast<int>(CSSPropertyID::kBorderBottom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str140), static_cast<int>(CSSPropertyID::kStrokeLinecap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str141), static_cast<int>(CSSPropertyID::kMaskPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str142), static_cast<int>(CSSPropertyID::kGridColumnEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str143), static_cast<int>(CSSPropertyID::kColumnRuleColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str144), static_cast<int>(CSSPropertyID::kGridRow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str145), static_cast<int>(CSSPropertyID::kGridTemplateColumns)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str146), static_cast<int>(CSSPropertyID::kMaskComposite)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str147), static_cast<int>(CSSPropertyID::kTransform)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str148), static_cast<int>(CSSPropertyID::kBorderInlineEndColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str149), static_cast<int>(CSSPropertyID::kPlaceSelf)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str150), static_cast<int>(CSSPropertyID::kMarkerEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str151), static_cast<int>(CSSPropertyID::kMaskMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str152), static_cast<int>(CSSPropertyID::kScrollPaddingTop)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str153), static_cast<int>(CSSPropertyID::kMarkerMid)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str154), static_cast<int>(CSSPropertyID::kAlignSelf)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str155), static_cast<int>(CSSPropertyID::kReadingOrderItems)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str156), static_cast<int>(CSSPropertyID::kFontPalette)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str157), static_cast<int>(CSSPropertyID::kWritingMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str158), static_cast<int>(CSSPropertyID::kBorderBottomColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str159), static_cast<int>(CSSPropertyID::kWordSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str160), static_cast<int>(CSSPropertyID::kMarginLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str161), static_cast<int>(CSSPropertyID::kBorderInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str162), static_cast<int>(CSSPropertyID::kGridRowStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str163), static_cast<int>(CSSPropertyID::kAliasGridRowGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str164), static_cast<int>(CSSPropertyID::kUnicodeBidi)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str165), static_cast<int>(CSSPropertyID::kLineBreak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str166), static_cast<int>(CSSPropertyID::kAnimationIterationCount)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str167), static_cast<int>(CSSPropertyID::kBorderRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str168), static_cast<int>(CSSPropertyID::kInsetBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str169), static_cast<int>(CSSPropertyID::kTransitionDuration)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str170), static_cast<int>(CSSPropertyID::kGridAutoColumns)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str171), static_cast<int>(CSSPropertyID::kFloodColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str172), static_cast<int>(CSSPropertyID::kTransformOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str173), static_cast<int>(CSSPropertyID::kAnimationTimeline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str174), static_cast<int>(CSSPropertyID::kGridRowEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str175), static_cast<int>(CSSPropertyID::kBorderInlineStartColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str176), static_cast<int>(CSSPropertyID::kMarginBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str177), static_cast<int>(CSSPropertyID::kGridTemplateRows)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str178), static_cast<int>(CSSPropertyID::kInternalForcedColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str179), static_cast<int>(CSSPropertyID::kFontOpticalSizing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str180), static_cast<int>(CSSPropertyID::kSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str181), static_cast<int>(CSSPropertyID::kInsetBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str182), static_cast<int>(CSSPropertyID::kZoom)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str183), static_cast<int>(CSSPropertyID::kFillRule)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str184), static_cast<int>(CSSPropertyID::kMarginBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str185), static_cast<int>(CSSPropertyID::kScrollStartBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str186), static_cast<int>(CSSPropertyID::kResize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str187), static_cast<int>(CSSPropertyID::kPageBreakInside)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str188), static_cast<int>(CSSPropertyID::kBreakInside)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str189), static_cast<int>(CSSPropertyID::kColumnFill)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str190), static_cast<int>(CSSPropertyID::kBorderStartEndRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str191), static_cast<int>(CSSPropertyID::kAnimationFillMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str192), static_cast<int>(CSSPropertyID::kObjectPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str193), static_cast<int>(CSSPropertyID::kScrollMarginBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str194), static_cast<int>(CSSPropertyID::kInsetBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str195), static_cast<int>(CSSPropertyID::kInlineSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str196), static_cast<int>(CSSPropertyID::kGridAutoRows)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str197), static_cast<int>(CSSPropertyID::kPaddingLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str198), static_cast<int>(CSSPropertyID::kScrollPaddingBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str199), static_cast<int>(CSSPropertyID::kBorderLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str200), static_cast<int>(CSSPropertyID::kBorderImageOutset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str201), static_cast<int>(CSSPropertyID::kScrollStartTargetBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str202), static_cast<int>(CSSPropertyID::kAlignmentBaseline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str203), static_cast<int>(CSSPropertyID::kMinInlineSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str204), static_cast<int>(CSSPropertyID::kScrollMarginBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str205), static_cast<int>(CSSPropertyID::kBorderTopWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str206), static_cast<int>(CSSPropertyID::kWidows)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str207), static_cast<int>(CSSPropertyID::kInternalAlignContentBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str208), static_cast<int>(CSSPropertyID::kPaddingBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str209), static_cast<int>(CSSPropertyID::kScrollPaddingBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str210), static_cast<int>(CSSPropertyID::kBorderLeftColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str211), static_cast<int>(CSSPropertyID::kScrollPaddingLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str212), static_cast<int>(CSSPropertyID::kBorderBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str213), static_cast<int>(CSSPropertyID::kScrollPaddingInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str214), static_cast<int>(CSSPropertyID::kAliasWordWrap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str215), static_cast<int>(CSSPropertyID::kMarginBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str216), static_cast<int>(CSSPropertyID::kScrollMarginBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str217), static_cast<int>(CSSPropertyID::kTabSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str218), static_cast<int>(CSSPropertyID::kBorderEndEndRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str219), static_cast<int>(CSSPropertyID::kBorderImageSlice)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str220), static_cast<int>(CSSPropertyID::kBackground)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str221), static_cast<int>(CSSPropertyID::kAnimationDuration)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str222), static_cast<int>(CSSPropertyID::kBorderBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str223), static_cast<int>(CSSPropertyID::kBorderStartStartRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str224), static_cast<int>(CSSPropertyID::kRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str225), static_cast<int>(CSSPropertyID::kColorInterpolationFilters)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str226), static_cast<int>(CSSPropertyID::kScrollPaddingBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str227), static_cast<int>(CSSPropertyID::kTransitionDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str228), static_cast<int>(CSSPropertyID::kOrphans)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str229), static_cast<int>(CSSPropertyID::kInherits)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str230), static_cast<int>(CSSPropertyID::kPaddingInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str231), static_cast<int>(CSSPropertyID::kTransitionTimingFunction)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str232), static_cast<int>(CSSPropertyID::kInternalForcedOutlineColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str233), static_cast<int>(CSSPropertyID::kBorderBlockColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str234), static_cast<int>(CSSPropertyID::kScrollPaddingInlineStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str235), static_cast<int>(CSSPropertyID::kClipPath)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str236), static_cast<int>(CSSPropertyID::kOffset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str237), static_cast<int>(CSSPropertyID::kBackgroundClip)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str238), static_cast<int>(CSSPropertyID::kFontKerning)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str239), static_cast<int>(CSSPropertyID::kBackgroundColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str240), static_cast<int>(CSSPropertyID::kStrokeLinejoin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str241), static_cast<int>(CSSPropertyID::kAnchorName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str242), static_cast<int>(CSSPropertyID::kBorderBlockEndColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str243), static_cast<int>(CSSPropertyID::kColorScheme)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str244), static_cast<int>(CSSPropertyID::kAliasEpubWritingMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str245), static_cast<int>(CSSPropertyID::kPaddingBlockEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str246), static_cast<int>(CSSPropertyID::kInternalForcedBorderColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str247), static_cast<int>(CSSPropertyID::kBorderEndStartRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str248), static_cast<int>(CSSPropertyID::kScrollPaddingInlineEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str249), static_cast<int>(CSSPropertyID::kWebkitLocale)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str250), static_cast<int>(CSSPropertyID::kStrokeMiterlimit)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str251), static_cast<int>(CSSPropertyID::kPositionAnchor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str252), static_cast<int>(CSSPropertyID::kShapeMargin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str253), static_cast<int>(CSSPropertyID::kMarginRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str254), static_cast<int>(CSSPropertyID::kLightingColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str255), static_cast<int>(CSSPropertyID::kOffsetRotate)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str256), static_cast<int>(CSSPropertyID::kScrollSnapAlign)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str257), static_cast<int>(CSSPropertyID::kFallback)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str258), static_cast<int>(CSSPropertyID::kWordBreak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str259), static_cast<int>(CSSPropertyID::kAliasEpubCaptionSide)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str260), static_cast<int>(CSSPropertyID::kAliasWebkitAnimation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str261), static_cast<int>(CSSPropertyID::kOffsetPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str262), static_cast<int>(CSSPropertyID::kAliasEpubWordBreak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str263), static_cast<int>(CSSPropertyID::kAliasWebkitAppearance)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str264), static_cast<int>(CSSPropertyID::kBreakAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str265), static_cast<int>(CSSPropertyID::kAliasWebkitOrder)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str266), static_cast<int>(CSSPropertyID::kBorderBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str267), static_cast<int>(CSSPropertyID::kPaddingBlockStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str268), static_cast<int>(CSSPropertyID::kAliasWebkitMarginStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str269), static_cast<int>(CSSPropertyID::kAliasWebkitAlignContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str270), static_cast<int>(CSSPropertyID::kShapeRendering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str271), static_cast<int>(CSSPropertyID::kScrollMarginLeft)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str272), static_cast<int>(CSSPropertyID::kBackgroundRepeat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str273), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str274), static_cast<int>(CSSPropertyID::kMaskSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str275), static_cast<int>(CSSPropertyID::kObjectFit)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str276), static_cast<int>(CSSPropertyID::kTouchAction)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str277), static_cast<int>(CSSPropertyID::kBorderBlockStartColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str278), static_cast<int>(CSSPropertyID::kOffsetDistance)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str279), static_cast<int>(CSSPropertyID::kPageBreakAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str280), static_cast<int>(CSSPropertyID::kY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str281), static_cast<int>(CSSPropertyID::kBackgroundPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str282), static_cast<int>(CSSPropertyID::kBorderTopLeftRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str283), static_cast<int>(CSSPropertyID::kRy)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str284), static_cast<int>(CSSPropertyID::kCy)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str285), static_cast<int>(CSSPropertyID::kAliasWebkitColumns)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str286), static_cast<int>(CSSPropertyID::kFontSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str287), static_cast<int>(CSSPropertyID::kPaddingRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str288), static_cast<int>(CSSPropertyID::kWebkitLineClamp)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str289), static_cast<int>(CSSPropertyID::kBackdropFilter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str290), static_cast<int>(CSSPropertyID::kOutlineOffset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str291), static_cast<int>(CSSPropertyID::kBorderRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str292), static_cast<int>(CSSPropertyID::kGridAutoFlow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str293), static_cast<int>(CSSPropertyID::kTypes)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str294), static_cast<int>(CSSPropertyID::kBreakBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str295), static_cast<int>(CSSPropertyID::kNegative)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str296), static_cast<int>(CSSPropertyID::kSystem)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str297), static_cast<int>(CSSPropertyID::kAliasWebkitUserSelect)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str298), static_cast<int>(CSSPropertyID::kOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str299), static_cast<int>(CSSPropertyID::kShapeOutside)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str300), static_cast<int>(CSSPropertyID::kAliasWebkitColumnSpan)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str301), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str302), static_cast<int>(CSSPropertyID::kBackgroundBlendMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str303), static_cast<int>(CSSPropertyID::kPerspective)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str304), static_cast<int>(CSSPropertyID::kNavigation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str305), static_cast<int>(CSSPropertyID::kAliasWebkitAlignItems)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str306), static_cast<int>(CSSPropertyID::kListStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str307), static_cast<int>(CSSPropertyID::kBlockSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str308), static_cast<int>(CSSPropertyID::kScrollPaddingRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str309), static_cast<int>(CSSPropertyID::kPointerEvents)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str310), static_cast<int>(CSSPropertyID::kBorderRightColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str311), static_cast<int>(CSSPropertyID::kAliasWebkitPaddingStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str312), static_cast<int>(CSSPropertyID::kEmptyCells)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str313), static_cast<int>(CSSPropertyID::kPositionTry)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str314), static_cast<int>(CSSPropertyID::kAliasWebkitBorderStart)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str315), static_cast<int>(CSSPropertyID::kVerticalAlign)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str316), static_cast<int>(CSSPropertyID::kStopOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str317), static_cast<int>(CSSPropertyID::kWhiteSpace)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str318), static_cast<int>(CSSPropertyID::kPageBreakBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str319), static_cast<int>(CSSPropertyID::kScrollStartY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str320), static_cast<int>(CSSPropertyID::kContainerType)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str321), static_cast<int>(CSSPropertyID::kWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str322), static_cast<int>(CSSPropertyID::kWillChange)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str323), static_cast<int>(CSSPropertyID::kMinBlockSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str324), static_cast<int>(CSSPropertyID::kDisplay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str325), static_cast<int>(CSSPropertyID::kLineGapOverride)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str326), static_cast<int>(CSSPropertyID::kFieldSizing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str327), static_cast<int>(CSSPropertyID::kContainIntrinsicSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str328), static_cast<int>(CSSPropertyID::kAliasWebkitPaddingEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str329), static_cast<int>(CSSPropertyID::kPositionTryOrder)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str330), static_cast<int>(CSSPropertyID::kForcedColorAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str331), static_cast<int>(CSSPropertyID::kAliasWebkitMask)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str332), static_cast<int>(CSSPropertyID::kAliasWebkitBorderStartColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str333), static_cast<int>(CSSPropertyID::kOverrideColors)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str334), static_cast<int>(CSSPropertyID::kMinWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str335), static_cast<int>(CSSPropertyID::kAscentOverride)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str336), static_cast<int>(CSSPropertyID::kSymbols)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str337), static_cast<int>(CSSPropertyID::kAnimationPlayState)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str338), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationIterationCount)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str339), static_cast<int>(CSSPropertyID::kAliasWebkitColumnRule)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str340), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationDuration)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str341), static_cast<int>(CSSPropertyID::kFontStretch)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str342), static_cast<int>(CSSPropertyID::kContainIntrinsicInlineSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str343), static_cast<int>(CSSPropertyID::kX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str344), static_cast<int>(CSSPropertyID::kAliasWebkitMaskSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str345), static_cast<int>(CSSPropertyID::kScrollStartTargetY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str346), static_cast<int>(CSSPropertyID::kAnimationDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str347), static_cast<int>(CSSPropertyID::kRx)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str348), static_cast<int>(CSSPropertyID::kCx)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str349), static_cast<int>(CSSPropertyID::kWhiteSpaceCollapse)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str350), static_cast<int>(CSSPropertyID::kBackgroundOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str351), static_cast<int>(CSSPropertyID::kInitialValue)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str352), static_cast<int>(CSSPropertyID::kAliasWebkitMaskRepeat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str353), static_cast<int>(CSSPropertyID::kBackgroundImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str354), static_cast<int>(CSSPropertyID::kAliasWebkitFilter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str355), static_cast<int>(CSSPropertyID::kWebkitUserDrag)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str356), static_cast<int>(CSSPropertyID::kInternalFontSizeDelta)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str357), static_cast<int>(CSSPropertyID::kAliasWebkitMaskPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str358), static_cast<int>(CSSPropertyID::kOutlineStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str359), static_cast<int>(CSSPropertyID::kAliasWebkitColumnRuleColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str360), static_cast<int>(CSSPropertyID::kSizeAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str361), static_cast<int>(CSSPropertyID::kOriginTrialTestProperty)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str362), static_cast<int>(CSSPropertyID::kAliasWebkitMaskComposite)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str363), static_cast<int>(CSSPropertyID::kFontFeatureSettings)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str364), static_cast<int>(CSSPropertyID::kAliasWebkitTransition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str365), static_cast<int>(CSSPropertyID::kAliasWebkitTransform)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str366), static_cast<int>(CSSPropertyID::kAliasWebkitColumnCount)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str367), static_cast<int>(CSSPropertyID::kBorderStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str368), static_cast<int>(CSSPropertyID::kAliasWebkitAppRegion)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str369), static_cast<int>(CSSPropertyID::kWebkitWritingMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str370), static_cast<int>(CSSPropertyID::kTransitionBehavior)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str371), static_cast<int>(CSSPropertyID::kScrollMarginRight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str372), static_cast<int>(CSSPropertyID::kTextAlign)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str373), static_cast<int>(CSSPropertyID::kListStylePosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str374), static_cast<int>(CSSPropertyID::kAliasWebkitMarginBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str375), static_cast<int>(CSSPropertyID::kPositionTryOptions)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str376), static_cast<int>(CSSPropertyID::kScrollStartX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str377), static_cast<int>(CSSPropertyID::kAliasWebkitBorderRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str378), static_cast<int>(CSSPropertyID::kTextSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str379), static_cast<int>(CSSPropertyID::kColumnWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str380), static_cast<int>(CSSPropertyID::kOutlineWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str381), static_cast<int>(CSSPropertyID::kScrollbarWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str382), static_cast<int>(CSSPropertyID::kTextOrientation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str383), static_cast<int>(CSSPropertyID::kBaselineShift)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str384), static_cast<int>(CSSPropertyID::kBorderBlockStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str385), static_cast<int>(CSSPropertyID::kInternalForcedBackgroundColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str386), static_cast<int>(CSSPropertyID::kBorderImageSource)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str387), static_cast<int>(CSSPropertyID::kAliasWebkitTransformOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str388), static_cast<int>(CSSPropertyID::kTextAlignLast)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str389), static_cast<int>(CSSPropertyID::kAliasWebkitMaskImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str390), static_cast<int>(CSSPropertyID::kTableLayout)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str391), static_cast<int>(CSSPropertyID::kBackgroundSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str392), static_cast<int>(CSSPropertyID::kAliasWebkitMarginEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str393), static_cast<int>(CSSPropertyID::kTextIndent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str394), static_cast<int>(CSSPropertyID::kViewTimeline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str395), static_cast<int>(CSSPropertyID::kRubyPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str396), static_cast<int>(CSSPropertyID::kWebkitRtlOrdering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str397), static_cast<int>(CSSPropertyID::kBorderWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str398), static_cast<int>(CSSPropertyID::kBorderImageWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str399), static_cast<int>(CSSPropertyID::kScrollTimelineAxis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str400), static_cast<int>(CSSPropertyID::kMaskType)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str401), static_cast<int>(CSSPropertyID::kTextDecoration)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str402), static_cast<int>(CSSPropertyID::kTextRendering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str403), static_cast<int>(CSSPropertyID::kScrollStartTargetX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str404), static_cast<int>(CSSPropertyID::kViewTimelineInset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str405), static_cast<int>(CSSPropertyID::kStrokeOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str406), static_cast<int>(CSSPropertyID::kAliasWebkitMarginAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str407), static_cast<int>(CSSPropertyID::kColumnRuleStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str408), static_cast<int>(CSSPropertyID::kViewTransitionName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str409), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationFillMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str410), static_cast<int>(CSSPropertyID::kFontVariant)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str411), static_cast<int>(CSSPropertyID::kViewTransitionClass)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str412), static_cast<int>(CSSPropertyID::kTextDecorationLine)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str413), static_cast<int>(CSSPropertyID::kFontStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str414), static_cast<int>(CSSPropertyID::kBorderInlineEndStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str415), static_cast<int>(CSSPropertyID::kTextDecorationColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str416), static_cast<int>(CSSPropertyID::kAliasWebkitPaddingAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str417), static_cast<int>(CSSPropertyID::kFillOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str418), static_cast<int>(CSSPropertyID::kTextAutospace)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str419), static_cast<int>(CSSPropertyID::kAnimationTimingFunction)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str420), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str421), static_cast<int>(CSSPropertyID::kHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str422), static_cast<int>(CSSPropertyID::kBorderInlineStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str423), static_cast<int>(CSSPropertyID::kStrokeWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str424), static_cast<int>(CSSPropertyID::kFontVariantCaps)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str425), static_cast<int>(CSSPropertyID::kFontWeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str426), static_cast<int>(CSSPropertyID::kWebkitBorderImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str427), static_cast<int>(CSSPropertyID::kBorderTopRightRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str428), static_cast<int>(CSSPropertyID::kWebkitColumnBreakInside)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str429), static_cast<int>(CSSPropertyID::kTransformStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str430), static_cast<int>(CSSPropertyID::kScrollSnapType)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str431), static_cast<int>(CSSPropertyID::kWebkitBorderVerticalSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str432), static_cast<int>(CSSPropertyID::kBorderBottomLeftRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str433), static_cast<int>(CSSPropertyID::kAliasWebkitBorderEnd)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str434), static_cast<int>(CSSPropertyID::kFontVariantPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str435), static_cast<int>(CSSPropertyID::kMinHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str436), static_cast<int>(CSSPropertyID::kLineHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str437), static_cast<int>(CSSPropertyID::kListStyleImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str438), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBeforeColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str439), static_cast<int>(CSSPropertyID::kViewTimelineName)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str440), static_cast<int>(CSSPropertyID::kTransitionProperty)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str441), static_cast<int>(CSSPropertyID::kBorderInlineEndWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str442), static_cast<int>(CSSPropertyID::kFontDisplay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str443), static_cast<int>(CSSPropertyID::kBorderInlineStartStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str444), static_cast<int>(CSSPropertyID::kFontVariantEastAsian)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str445), static_cast<int>(CSSPropertyID::kFloodOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str446), static_cast<int>(CSSPropertyID::kWebkitPrintColorAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str447), static_cast<int>(CSSPropertyID::kAliasWebkitTransitionDuration)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str448), static_cast<int>(CSSPropertyID::kFontVariationSettings)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str449), static_cast<int>(CSSPropertyID::kOffsetPath)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str450), static_cast<int>(CSSPropertyID::kTextWrap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str451), static_cast<int>(CSSPropertyID::kAliasWebkitMaskClip)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str452), static_cast<int>(CSSPropertyID::kAliasWebkitBorderEndColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str453), static_cast<int>(CSSPropertyID::kOffsetAnchor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str454), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationTimingFunction)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str455), static_cast<int>(CSSPropertyID::kAliasWebkitPaddingBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str456), static_cast<int>(CSSPropertyID::kAliasWebkitBorderAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str457), static_cast<int>(CSSPropertyID::kMathDepth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str458), static_cast<int>(CSSPropertyID::kMixBlendMode)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str459), static_cast<int>(CSSPropertyID::kFlex)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str460), static_cast<int>(CSSPropertyID::kAliasWebkitMaskOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str461), static_cast<int>(CSSPropertyID::kInternalVisitedColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str462), static_cast<int>(CSSPropertyID::kTextUnderlinePosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str463), static_cast<int>(CSSPropertyID::kContainIntrinsicBlockSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str464), static_cast<int>(CSSPropertyID::kBorderBottomStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str465), static_cast<int>(CSSPropertyID::kDescentOverride)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str466), static_cast<int>(CSSPropertyID::kPrefix)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str467), static_cast<int>(CSSPropertyID::kDynamicRangeLimit)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str468), static_cast<int>(CSSPropertyID::kBorderRightStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str469), static_cast<int>(CSSPropertyID::kBorderInlineStartWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str470), static_cast<int>(CSSPropertyID::kAliasWebkitBorderAfterColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str471), static_cast<int>(CSSPropertyID::kBackgroundAttachment)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str472), static_cast<int>(CSSPropertyID::kFontVariantNumeric)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str473), static_cast<int>(CSSPropertyID::kContainIntrinsicWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str474), static_cast<int>(CSSPropertyID::kAliasWebkitAlignSelf)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str475), static_cast<int>(CSSPropertyID::kColumnRuleWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str476), static_cast<int>(CSSPropertyID::kAliasEpubTextOrientation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str477), static_cast<int>(CSSPropertyID::kInternalVisitedCaretColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str478), static_cast<int>(CSSPropertyID::kInternalForcedVisitedColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str479), static_cast<int>(CSSPropertyID::kOverflow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str480), static_cast<int>(CSSPropertyID::kFontVariantAlternates)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str481), static_cast<int>(CSSPropertyID::kWebkitLineBreak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str482), static_cast<int>(CSSPropertyID::kBorderTopStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str483), static_cast<int>(CSSPropertyID::kTextTransform)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str484), static_cast<int>(CSSPropertyID::kBorderLeftStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str485), static_cast<int>(CSSPropertyID::kBufferedRendering)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str486), static_cast<int>(CSSPropertyID::kAliasWebkitClipPath)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str487), static_cast<int>(CSSPropertyID::kAliasWebkitColumnGap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str488), static_cast<int>(CSSPropertyID::kOverflowInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str489), static_cast<int>(CSSPropertyID::kTextSpacingTrim)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str490), static_cast<int>(CSSPropertyID::kFlexDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str491), static_cast<int>(CSSPropertyID::kAliasWebkitShapeMargin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str492), static_cast<int>(CSSPropertyID::kFlexBasis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str493), static_cast<int>(CSSPropertyID::kBorderBlockWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str494), static_cast<int>(CSSPropertyID::kMathStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str495), static_cast<int>(CSSPropertyID::kWebkitColumnBreakAfter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str496), static_cast<int>(CSSPropertyID::kStrokeDasharray)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str497), static_cast<int>(CSSPropertyID::kWebkitBorderHorizontalSpacing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str498), static_cast<int>(CSSPropertyID::kBorderBlockEndStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str499), static_cast<int>(CSSPropertyID::kBorderInlineWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str500), static_cast<int>(CSSPropertyID::kFontSizeAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str501), static_cast<int>(CSSPropertyID::kTransformBox)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str502), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBottomLeftRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str503), static_cast<int>(CSSPropertyID::kVectorEffect)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str504), static_cast<int>(CSSPropertyID::kInternalVisitedOutlineColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str505), static_cast<int>(CSSPropertyID::kPerspectiveOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str506), static_cast<int>(CSSPropertyID::kFontFamily)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str507), static_cast<int>(CSSPropertyID::kAliasWebkitOpacity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str508), static_cast<int>(CSSPropertyID::kInternalOverflowInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str509), static_cast<int>(CSSPropertyID::kBorderBottomRightRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str510), static_cast<int>(CSSPropertyID::kZIndex)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str511), static_cast<int>(CSSPropertyID::kFontVariantEmoji)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str512), static_cast<int>(CSSPropertyID::kMaxInlineSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str513), static_cast<int>(CSSPropertyID::kMathShift)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str514), static_cast<int>(CSSPropertyID::kBorderBlockEndWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str515), static_cast<int>(CSSPropertyID::kInternalVisitedBorderTopColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str516), static_cast<int>(CSSPropertyID::kScrollBehavior)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str517), static_cast<int>(CSSPropertyID::kWebkitTransformOriginZ)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str518), static_cast<int>(CSSPropertyID::kWebkitColumnBreakBefore)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str519), static_cast<int>(CSSPropertyID::kAliasWebkitBorderTopLeftRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str520), static_cast<int>(CSSPropertyID::kInternalVisitedStroke)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str521), static_cast<int>(CSSPropertyID::kBorderLeftWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str522), static_cast<int>(CSSPropertyID::kBorderBottomWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str523), static_cast<int>(CSSPropertyID::kFlexWrap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str524), static_cast<int>(CSSPropertyID::kBorderBlockStartStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str525), static_cast<int>(CSSPropertyID::kOverflowClipMargin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str526), static_cast<int>(CSSPropertyID::kAliasWebkitTransitionTimingFunction)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str527), static_cast<int>(CSSPropertyID::kFlexGrow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str528), static_cast<int>(CSSPropertyID::kBoxSizing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str529), static_cast<int>(CSSPropertyID::kInternalVisitedFill)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str530), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationPlayState)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str531), static_cast<int>(CSSPropertyID::kBackgroundPositionY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str532), static_cast<int>(CSSPropertyID::kInternalVisitedBorderInlineStartColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str533), static_cast<int>(CSSPropertyID::kOverlay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str534), static_cast<int>(CSSPropertyID::kJustifyItems)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str535), static_cast<int>(CSSPropertyID::kTextAnchor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str536), static_cast<int>(CSSPropertyID::kAliasWebkitPerspective)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str537), static_cast<int>(CSSPropertyID::kAliasWebkitAnimationDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str538), static_cast<int>(CSSPropertyID::kAliasWebkitLogicalWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str539), static_cast<int>(CSSPropertyID::kJustifyContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str540), static_cast<int>(CSSPropertyID::kInternalVisitedColumnRuleColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str541), static_cast<int>(CSSPropertyID::kAliasWebkitFontFeatureSettings)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str542), static_cast<int>(CSSPropertyID::kAliasEpubTextTransform)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str543), static_cast<int>(CSSPropertyID::kContainIntrinsicHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str544), static_cast<int>(CSSPropertyID::kOverflowWrap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str545), static_cast<int>(CSSPropertyID::kTextEmphasis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str546), static_cast<int>(CSSPropertyID::kBorderBlockStartWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str547), static_cast<int>(CSSPropertyID::kWebkitFontSmoothing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str548), static_cast<int>(CSSPropertyID::kInternalVisitedBorderInlineEndColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str549), static_cast<int>(CSSPropertyID::kInternalVisitedBorderBottomColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str550), static_cast<int>(CSSPropertyID::kBorderRightWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str551), static_cast<int>(CSSPropertyID::kAliasWebkitPerspectiveOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str552), static_cast<int>(CSSPropertyID::kTextEmphasisColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str553), static_cast<int>(CSSPropertyID::kListStyleType)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str554), static_cast<int>(CSSPropertyID::kTextDecorationSkipInk)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str555), static_cast<int>(CSSPropertyID::kAliasEpubTextCombine)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str556), static_cast<int>(CSSPropertyID::kTextEmphasisPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str557), static_cast<int>(CSSPropertyID::kAliasWebkitBorderStartStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str558), static_cast<int>(CSSPropertyID::kSuffix)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str559), static_cast<int>(CSSPropertyID::kVisibility)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str560), static_cast<int>(CSSPropertyID::kWebkitRubyPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str561), static_cast<int>(CSSPropertyID::kOverflowBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str562), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBottomRightRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str563), static_cast<int>(CSSPropertyID::kWebkitBoxPack)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str564), static_cast<int>(CSSPropertyID::kFontSynthesis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str565), static_cast<int>(CSSPropertyID::kBackgroundPositionX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str566), static_cast<int>(CSSPropertyID::kWebkitTextCombine)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str567), static_cast<int>(CSSPropertyID::kAliasWebkitColumnRuleStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str568), static_cast<int>(CSSPropertyID::kAliasWebkitBorderStartWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str569), static_cast<int>(CSSPropertyID::kSyntax)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str570), static_cast<int>(CSSPropertyID::kWebkitMaskPositionY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str571), static_cast<int>(CSSPropertyID::kPositionVisibility)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str572), static_cast<int>(CSSPropertyID::kOverscrollBehavior)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str573), static_cast<int>(CSSPropertyID::kInternalOverflowBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str574), static_cast<int>(CSSPropertyID::kAliasWebkitBorderTopRightRadius)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str575), static_cast<int>(CSSPropertyID::kInternalVisitedBorderLeftColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str576), static_cast<int>(CSSPropertyID::kAliasWebkitTransformStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str577), static_cast<int>(CSSPropertyID::kOverscrollBehaviorInline)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str578), static_cast<int>(CSSPropertyID::kMaxBlockSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str579), static_cast<int>(CSSPropertyID::kFlexFlow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str580), static_cast<int>(CSSPropertyID::kAliasWebkitColumnRuleWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str581), static_cast<int>(CSSPropertyID::kFontSynthesisSmallCaps)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str582), static_cast<int>(CSSPropertyID::kAliasWebkitTransitionProperty)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str583), static_cast<int>(CSSPropertyID::kWebkitTransformOriginY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str584), static_cast<int>(CSSPropertyID::kMaxWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str585), static_cast<int>(CSSPropertyID::kAdditiveSymbols)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str586), static_cast<int>(CSSPropertyID::kTextUnderlineOffset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str587), static_cast<int>(CSSPropertyID::kJustifySelf)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str588), static_cast<int>(CSSPropertyID::kTextShadow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str589), static_cast<int>(CSSPropertyID::kAliasWebkitTransitionDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str590), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImage)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str591), static_cast<int>(CSSPropertyID::kAliasWebkitLogicalHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str592), static_cast<int>(CSSPropertyID::kTextCombineUpright)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str593), static_cast<int>(CSSPropertyID::kStrokeDashoffset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str594), static_cast<int>(CSSPropertyID::kInternalVisitedBackgroundColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str595), static_cast<int>(CSSPropertyID::kFontVariantLigatures)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str596), static_cast<int>(CSSPropertyID::kAliasWebkitFlex)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str597), static_cast<int>(CSSPropertyID::kViewTimelineAxis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str598), static_cast<int>(CSSPropertyID::kWebkitTextStroke)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str599), static_cast<int>(CSSPropertyID::kWebkitUserModify)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str600), static_cast<int>(CSSPropertyID::kAliasWebkitBackgroundClip)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str601), static_cast<int>(CSSPropertyID::kInternalVisitedBorderBlockStartColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str602), static_cast<int>(CSSPropertyID::kAliasWebkitMinLogicalWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str603), static_cast<int>(CSSPropertyID::kWebkitTapHighlightColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str604), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImageSlice)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str605), static_cast<int>(CSSPropertyID::kTextDecorationStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str606), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImageRepeat)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str607), static_cast<int>(CSSPropertyID::kAliasWebkitBackgroundOrigin)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str608), static_cast<int>(CSSPropertyID::kWebkitMaskPositionX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str609), static_cast<int>(CSSPropertyID::kWebkitTextStrokeColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str610), static_cast<int>(CSSPropertyID::kOverflowAnchor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str611), static_cast<int>(CSSPropertyID::kInternalVisitedBorderBlockEndColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str612), static_cast<int>(CSSPropertyID::kHyphens)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str613), static_cast<int>(CSSPropertyID::kBoxShadow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str614), static_cast<int>(CSSPropertyID::kAliasEpubTextEmphasis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str615), static_cast<int>(CSSPropertyID::kWebkitTextFillColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str616), static_cast<int>(CSSPropertyID::kWebkitTextOrientation)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str617), static_cast<int>(CSSPropertyID::kTextDecorationThickness)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str618), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBeforeStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str619), static_cast<int>(CSSPropertyID::kAliasEpubTextEmphasisColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str620), static_cast<int>(CSSPropertyID::kWebkitTransformOriginX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str621), static_cast<int>(CSSPropertyID::kAliasWebkitFlexDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str622), static_cast<int>(CSSPropertyID::kAliasWebkitBorderEndStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str623), static_cast<int>(CSSPropertyID::kWebkitBoxOrient)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str624), static_cast<int>(CSSPropertyID::kAliasWebkitFlexBasis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str625), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImageSource)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str626), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImageOutset)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str627), static_cast<int>(CSSPropertyID::kWebkitBoxReflect)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str628), static_cast<int>(CSSPropertyID::kContentVisibility)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str629), static_cast<int>(CSSPropertyID::kAliasWebkitBorderBeforeWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str630), static_cast<int>(CSSPropertyID::kTextSizeAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str631), static_cast<int>(CSSPropertyID::kTextBoxTrim)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str632), static_cast<int>(CSSPropertyID::kAliasWebkitBorderAfterStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str633), static_cast<int>(CSSPropertyID::kInternalVisitedBorderRightColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str634), static_cast<int>(CSSPropertyID::kAliasWebkitBorderEndWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str635), static_cast<int>(CSSPropertyID::kAliasWebkitShapeOutside)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str636), static_cast<int>(CSSPropertyID::kWebkitBoxDirection)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str637), static_cast<int>(CSSPropertyID::kInternalEmptyLineHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str638), static_cast<int>(CSSPropertyID::kFlexShrink)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str639), static_cast<int>(CSSPropertyID::kOverflowY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str640), static_cast<int>(CSSPropertyID::kTextBoxEdge)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str641), static_cast<int>(CSSPropertyID::kAliasWebkitBorderAfterWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str642), static_cast<int>(CSSPropertyID::kMaxHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str643), static_cast<int>(CSSPropertyID::kAliasWebkitTextSizeAdjust)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str644), static_cast<int>(CSSPropertyID::kOverscrollBehaviorBlock)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str645), static_cast<int>(CSSPropertyID::kAliasWebkitFlexGrow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str646), static_cast<int>(CSSPropertyID::kAliasWebkitMinLogicalHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str647), static_cast<int>(CSSPropertyID::kWebkitBoxDecorationBreak)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str648), static_cast<int>(CSSPropertyID::kShapeImageThreshold)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str649), static_cast<int>(CSSPropertyID::kAliasWebkitJustifyContent)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str650), static_cast<int>(CSSPropertyID::kAliasWebkitTextEmphasis)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str651), static_cast<int>(CSSPropertyID::kAliasWebkitFlexWrap)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str652), static_cast<int>(CSSPropertyID::kWebkitBoxOrdinalGroup)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str653), static_cast<int>(CSSPropertyID::kAliasWebkitTextEmphasisColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str654), static_cast<int>(CSSPropertyID::kWebkitBoxAlign)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str655), static_cast<int>(CSSPropertyID::kOverflowX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str656), static_cast<int>(CSSPropertyID::kAliasWebkitTextEmphasisPosition)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str657), static_cast<int>(CSSPropertyID::kTextOverflow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str658), static_cast<int>(CSSPropertyID::kAliasWebkitBackgroundSize)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str659), static_cast<int>(CSSPropertyID::kPopoverHideDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str660), static_cast<int>(CSSPropertyID::kAliasWebkitColumnWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str661), static_cast<int>(CSSPropertyID::kInternalVisitedTextDecorationColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str662), static_cast<int>(CSSPropertyID::kBackfaceVisibility)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str663), static_cast<int>(CSSPropertyID::kWebkitPerspectiveOriginY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str664), static_cast<int>(CSSPropertyID::kAliasWebkitFlexFlow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str665), static_cast<int>(CSSPropertyID::kWebkitTextDecorationsInEffect)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str666), static_cast<int>(CSSPropertyID::kAliasWebkitBoxSizing)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str667), static_cast<int>(CSSPropertyID::kObjectViewBox)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str668), static_cast<int>(CSSPropertyID::kPopoverShowDelay)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str669), static_cast<int>(CSSPropertyID::kAliasWebkitBoxShadow)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str670), static_cast<int>(CSSPropertyID::kAliasWebkitShapeImageThreshold)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str671), static_cast<int>(CSSPropertyID::kTextEmphasisStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str672), static_cast<int>(CSSPropertyID::kInternalVisitedTextStrokeColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str673), static_cast<int>(CSSPropertyID::kFontSynthesisStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str674), static_cast<int>(CSSPropertyID::kOverscrollBehaviorY)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str675), static_cast<int>(CSSPropertyID::kInternalVisitedTextFillColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str676), static_cast<int>(CSSPropertyID::kWebkitPerspectiveOriginX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str677), static_cast<int>(CSSPropertyID::kWebkitTextSecurity)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str678), static_cast<int>(CSSPropertyID::kHyphenateLimitChars)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str679), static_cast<int>(CSSPropertyID::kHyphenateCharacter)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str680), static_cast<int>(CSSPropertyID::kOverscrollBehaviorX)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str681), static_cast<int>(CSSPropertyID::kAliasWebkitFlexShrink)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str682), static_cast<int>(CSSPropertyID::kWebkitMaskBoxImageWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str683), static_cast<int>(CSSPropertyID::kAliasEpubTextEmphasisStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str684), static_cast<int>(CSSPropertyID::kWebkitTextStrokeWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str685), static_cast<int>(CSSPropertyID::kWebkitBoxFlex)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str686), static_cast<int>(CSSPropertyID::kAliasWebkitMaxLogicalWidth)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str687), static_cast<int>(CSSPropertyID::kInternalVisitedTextEmphasisColor)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str688), static_cast<int>(CSSPropertyID::kAliasWebkitTextEmphasisStyle)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str689), static_cast<int>(CSSPropertyID::kAliasWebkitMaxLogicalHeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str690), static_cast<int>(CSSPropertyID::kFontSynthesisWeight)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str691), static_cast<int>(CSSPropertyID::kAliasWebkitBackfaceVisibility)},
      {offsetof(struct CSSPropStringPool_t, CSSPropStringPool_str692), static_cast<int>(CSSPropertyID::kAliasWebkitHyphenateCharacter)}
    };

  static const short lookup[] =
    {
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
        0,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,   1,   2,  -1,  -1,  -1,   3,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,   4,  -1,
       -1,   5,  -1,  -1,   6,   7,  -1,   8,  -1,  -1,
       -1,  -1,   9,  -1,  -1,  10,  -1,  -1,  -1,  -1,
       -1,  -1,  11,  -1,  -1,  12,  13,  14,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  15,  16,  -1,  17,  -1,  18,  19,  -1,
       -1,  -1,  20,  -1,  -1,  -1,  -1,  -1,  21,  -1,
       22,  23,  24,  -1,  -1,  -1,  25,  -1,  -1,  -1,
       -1,  26,  -1,  27,  -1,  28,  -1,  29,  30,  -1,
       -1,  -1,  31,  32,  33,  -1,  -1,  -1,  -1,  34,
       35,  -1,  -1,  36,  -1,  37,  38,  39,  -1,  40,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       41,  42,  43,  -1,  -1,  44,  -1,  45,  46,  47,
       -1,  48,  -1,  -1,  -1,  -1,  49,  -1,  -1,  50,
       -1,  -1,  -1,  51,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  52,  -1,  -1,  -1,  53,  -1,
       -1,  54,  -1,  -1,  -1,  55,  -1,  -1,  -1,  56,
       -1,  -1,  -1,  57,  -1,  -1,  -1,  58,  59,  60,
       -1,  -1,  -1,  -1,  61,  -1,  62,  63,  -1,  64,
       -1,  -1,  65,  -1,  66,  -1,  -1,  -1,  -1,  -1,
       67,  -1,  -1,  -1,  -1,  -1,  68,  -1,  69,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  70,  -1,  71,  -1,
       72,  -1,  -1,  73,  -1,  -1,  -1,  -1,  -1,  74,
       -1,  -1,  -1,  -1,  -1,  -1,  75,  -1,  -1,  76,
       77,  78,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       79,  80,  -1,  -1,  -1,  81,  82,  83,  -1,  84,
       -1,  -1,  -1,  -1,  85,  86,  -1,  -1,  87,  -1,
       -1,  -1,  -1,  -1,  88,  -1,  -1,  89,  -1,  90,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  91,  92,
       93,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  94,  95,  96,  -1,  -1,  -1,  97,  -1,  -1,
       -1,  -1,  -1,  98,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  99,  -1, 100,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 101,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 102,  -1,  -1, 103,  -1,
       -1,  -1,  -1, 104,  -1, 105, 106,  -1, 107,  -1,
       -1,  -1,  -1, 108,  -1, 109,  -1, 110,  -1,  -1,
       -1,  -1, 111, 112, 113,  -1,  -1,  -1,  -1,  -1,
       -1, 114, 115,  -1,  -1, 116,  -1,  -1,  -1,  -1,
       -1,  -1, 117,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 118,  -1,  -1,  -1, 119,
      120,  -1,  -1,  -1,  -1, 121,  -1,  -1, 122, 123,
      124, 125,  -1, 126,  -1,  -1, 127, 128, 129,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 130,  -1,  -1,  -1,
      131,  -1,  -1,  -1,  -1,  -1, 132, 133, 134,  -1,
      135,  -1,  -1,  -1, 136,  -1,  -1,  -1, 137, 138,
      139,  -1,  -1,  -1,  -1,  -1, 140, 141,  -1,  -1,
       -1,  -1, 142, 143,  -1, 144,  -1,  -1,  -1, 145,
       -1,  -1,  -1,  -1,  -1,  -1, 146,  -1,  -1,  -1,
       -1,  -1,  -1, 147,  -1,  -1,  -1, 148,  -1,  -1,
      149,  -1,  -1,  -1,  -1,  -1, 150, 151,  -1,  -1,
       -1,  -1, 152,  -1, 153, 154, 155,  -1,  -1,  -1,
      156,  -1,  -1,  -1,  -1,  -1, 157,  -1, 158,  -1,
      159,  -1, 160,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 161, 162,  -1,  -1,  -1, 163, 164,  -1,
       -1,  -1, 165,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      166,  -1, 167,  -1, 168,  -1,  -1,  -1,  -1,  -1,
       -1, 169,  -1,  -1,  -1,  -1, 170,  -1,  -1,  -1,
      171,  -1,  -1,  -1,  -1, 172,  -1,  -1,  -1, 173,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 174,
       -1,  -1,  -1, 175, 176,  -1, 177,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 178,  -1,  -1,  -1, 179,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      180,  -1,  -1,  -1,  -1, 181, 182,  -1, 183,  -1,
       -1,  -1,  -1,  -1,  -1, 184,  -1,  -1, 185,  -1,
      186,  -1,  -1,  -1,  -1,  -1,  -1, 187, 188,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 189,
       -1, 190,  -1,  -1,  -1,  -1,  -1,  -1, 191,  -1,
       -1,  -1, 192,  -1,  -1,  -1,  -1, 193, 194,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 195,  -1, 196,  -1, 197,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 198,  -1,  -1, 199,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 200,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 201, 202,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 203,  -1,  -1, 204,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 205,  -1,  -1, 206,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 207, 208,  -1,
       -1,  -1,  -1, 209,  -1,  -1, 210,  -1,  -1, 211,
      212,  -1,  -1,  -1,  -1, 213,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 214,  -1,  -1,  -1, 215,  -1,  -1,
       -1, 216, 217, 218,  -1,  -1,  -1,  -1,  -1, 219,
      220,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 221,  -1,
       -1, 222,  -1, 223, 224,  -1, 225,  -1,  -1, 226,
       -1,  -1,  -1, 227,  -1, 228, 229,  -1,  -1,  -1,
       -1, 230,  -1,  -1, 231,  -1,  -1,  -1, 232,  -1,
      233,  -1,  -1, 234,  -1,  -1,  -1,  -1,  -1,  -1,
      235,  -1,  -1,  -1,  -1,  -1, 236,  -1, 237,  -1,
       -1,  -1, 238,  -1, 239,  -1, 240,  -1,  -1, 241,
       -1,  -1, 242,  -1,  -1, 243,  -1,  -1,  -1,  -1,
      244, 245,  -1,  -1,  -1, 246,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 247,  -1,  -1,  -1, 248,
       -1,  -1,  -1, 249, 250,  -1,  -1,  -1, 251, 252,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 253,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 254,  -1,
       -1, 255,  -1, 256,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 257,  -1,  -1, 258,  -1, 259,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 260,  -1,  -1,  -1, 261,  -1,
       -1, 262,  -1, 263,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 264, 265, 266,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 267,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      268,  -1,  -1,  -1,  -1,  -1,  -1, 269,  -1,  -1,
       -1, 270, 271,  -1,  -1,  -1, 272, 273,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 274,  -1, 275,  -1,  -1,
       -1, 276,  -1,  -1, 277,  -1,  -1,  -1, 278,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 279,  -1,  -1, 280,
       -1,  -1,  -1, 281,  -1,  -1,  -1,  -1, 282, 283,
      284,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 285,  -1,  -1, 286,  -1,
       -1,  -1,  -1, 287,  -1, 288,  -1,  -1,  -1, 289,
      290,  -1,  -1,  -1,  -1, 291,  -1, 292,  -1,  -1,
      293,  -1,  -1,  -1,  -1, 294,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 295, 296,  -1,  -1, 297,  -1,
       -1, 298,  -1, 299, 300,  -1,  -1,  -1,  -1,  -1,
      301, 302,  -1,  -1,  -1,  -1, 303,  -1, 304, 305,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 306,  -1, 307,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      308, 309,  -1,  -1,  -1, 310,  -1, 311,  -1,  -1,
      312,  -1, 313,  -1,  -1,  -1, 314,  -1, 315,  -1,
       -1,  -1, 316,  -1,  -1,  -1, 317,  -1,  -1,  -1,
      318, 319,  -1,  -1, 320, 321,  -1,  -1, 322, 323,
       -1,  -1,  -1,  -1,  -1, 324,  -1,  -1,  -1,  -1,
      325,  -1, 326,  -1, 327,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 328,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 329, 330, 331,  -1,  -1, 332,  -1,  -1,
       -1,  -1,  -1,  -1, 333, 334,  -1,  -1,  -1, 335,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 336,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 337,  -1, 338,  -1, 339,  -1,  -1,
       -1,  -1,  -1,  -1, 340, 341, 342, 343,  -1, 344,
       -1, 345,  -1, 346,  -1,  -1,  -1, 347, 348, 349,
       -1,  -1,  -1, 350,  -1,  -1,  -1, 351,  -1, 352,
      353,  -1,  -1,  -1,  -1,  -1, 354,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 355,  -1, 356,
       -1,  -1,  -1,  -1, 357,  -1,  -1, 358, 359,  -1,
       -1,  -1, 360,  -1, 361,  -1,  -1, 362,  -1,  -1,
       -1,  -1, 363,  -1,  -1,  -1,  -1,  -1,  -1, 364,
       -1,  -1,  -1,  -1,  -1, 365,  -1,  -1,  -1, 366,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 367,  -1,  -1,  -1,  -1, 368,
       -1,  -1,  -1,  -1,  -1,  -1, 369,  -1,  -1,  -1,
       -1,  -1, 370, 371,  -1, 372,  -1,  -1,  -1,  -1,
      373,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 374,  -1,  -1,  -1, 375,  -1,  -1,  -1, 376,
       -1,  -1,  -1,  -1, 377,  -1, 378,  -1, 379, 380,
      381,  -1,  -1,  -1,  -1, 382, 383,  -1,  -1,  -1,
       -1, 384,  -1,  -1, 385,  -1, 386,  -1, 387,  -1,
       -1,  -1, 388, 389,  -1,  -1,  -1, 390,  -1,  -1,
      391,  -1,  -1,  -1, 392, 393,  -1,  -1, 394,  -1,
       -1, 395, 396,  -1,  -1,  -1, 397,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 398,  -1, 399,
       -1,  -1, 400,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 401,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 402,  -1,  -1, 403,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 404,  -1,  -1, 405,  -1,  -1,  -1, 406,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 407,  -1,  -1,
       -1, 408, 409, 410,  -1,  -1,  -1,  -1, 411,  -1,
      412, 413,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 414,
      415,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 416,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 417,  -1, 418,
       -1,  -1, 419,  -1,  -1,  -1,  -1, 420,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 421,  -1, 422,  -1, 423,
       -1, 424, 425,  -1, 426, 427,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 428,  -1,  -1, 429,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 430, 431,  -1, 432,  -1,  -1,  -1,  -1,  -1,
      433,  -1,  -1, 434,  -1, 435, 436, 437, 438,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 439,  -1,  -1,  -1,
      440, 441,  -1, 442,  -1, 443,  -1,  -1, 444,  -1,
      445,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 446, 447,
       -1,  -1, 448,  -1,  -1,  -1,  -1,  -1, 449, 450,
       -1,  -1, 451,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 452, 453,  -1,  -1,  -1,  -1,  -1,  -1, 454,
       -1,  -1,  -1, 455,  -1, 456, 457,  -1,  -1,  -1,
      458,  -1,  -1, 459,  -1,  -1,  -1, 460,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 461,  -1,  -1,
       -1,  -1, 462, 463, 464, 465,  -1,  -1,  -1,  -1,
      466,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 467,  -1,  -1,  -1, 468, 469,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 470,  -1,  -1,  -1,
       -1,  -1,  -1, 471,  -1,  -1,  -1,  -1, 472, 473,
      474, 475, 476,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 477,  -1,  -1,  -1,  -1,  -1, 478,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 479,  -1,  -1, 480, 481,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 482,  -1,
       -1,  -1, 483,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      484,  -1, 485,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      486,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 487,  -1, 488, 489,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 490,
       -1,  -1,  -1, 491,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      492,  -1, 493,  -1,  -1,  -1,  -1, 494,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 495, 496,  -1, 497,
       -1,  -1,  -1,  -1, 498,  -1,  -1,  -1, 499,  -1,
       -1, 500,  -1,  -1,  -1,  -1,  -1,  -1, 501,  -1,
      502,  -1,  -1, 503,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 504,  -1,  -1,  -1,  -1,  -1,  -1, 505,  -1,
       -1,  -1,  -1,  -1,  -1, 506,  -1,  -1,  -1,  -1,
      507,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      508,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 509,  -1,  -1, 510, 511,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 512,  -1,  -1,  -1, 513, 514,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 515,  -1,  -1,  -1,
       -1,  -1,  -1, 516,  -1, 517,  -1,  -1,  -1,  -1,
      518,  -1,  -1,  -1,  -1,  -1,  -1, 519,  -1,  -1,
      520,  -1,  -1,  -1, 521, 522,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 523,  -1,  -1, 524,  -1,  -1,  -1,
       -1, 525,  -1,  -1, 526, 527,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 528,  -1,
       -1,  -1, 529,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 530, 531,  -1,  -1,  -1,  -1,  -1, 532, 533,
       -1,  -1,  -1,  -1, 534, 535, 536,  -1,  -1, 537,
       -1, 538,  -1,  -1,  -1,  -1, 539,  -1,  -1,  -1,
       -1,  -1, 540,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 541,  -1, 542,  -1, 543,
       -1,  -1,  -1, 544,  -1, 545,  -1,  -1, 546,  -1,
       -1, 547,  -1,  -1, 548,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 549,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 550,  -1, 551,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 552,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 553,  -1,  -1,  -1,  -1,  -1,
      554,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 555,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 556,  -1,  -1,  -1,  -1, 557,
       -1,  -1,  -1,  -1, 558,  -1,  -1,  -1, 559,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 560,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 561,  -1,  -1,  -1,  -1, 562,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 563,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 564,
      565,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 566,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      567, 568,  -1,  -1, 569,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 570,  -1,  -1,  -1,  -1,  -1, 571,
       -1,  -1,  -1, 572,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 573,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 574, 575,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 576,  -1,
       -1,  -1,  -1,  -1,  -1, 577,  -1,  -1, 578, 579,
       -1,  -1, 580,  -1,  -1,  -1, 581,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 582,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 583,  -1,  -1,
       -1,  -1,  -1,  -1, 584,  -1,  -1, 585,  -1, 586,
      587,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 588,
       -1,  -1,  -1,  -1, 589, 590,  -1,  -1,  -1,  -1,
       -1, 591,  -1,  -1, 592,  -1, 593, 594,  -1,  -1,
       -1,  -1,  -1, 595,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 596, 597,  -1, 598,  -1,  -1, 599,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 600,  -1,  -1, 601,  -1,  -1,  -1,  -1,
      602,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 603, 604,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 605,  -1,  -1,  -1, 606, 607,  -1,  -1,
       -1, 608,  -1,  -1,  -1,  -1, 609,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 610,
       -1, 611, 612,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 613,  -1, 614,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 615, 616,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 617,
      618,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 619,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 620,  -1,  -1,  -1,  -1,
       -1, 621,  -1, 622,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 623,  -1,  -1,  -1,  -1, 624,  -1,  -1,
       -1, 625, 626,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 627,  -1, 628,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 629,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 630,  -1, 631,  -1,  -1,  -1, 632,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 633,  -1,  -1, 634,  -1, 635,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 636,  -1,  -1, 637,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 638,  -1,
       -1,  -1, 639,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 640,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      641,  -1,  -1,  -1, 642, 643,  -1,  -1,  -1,  -1,
       -1,  -1, 644,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 645,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      646,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 647,  -1, 648,  -1,  -1, 649,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 650,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 651,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 652,  -1,  -1,  -1,  -1,  -1,  -1, 653,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 654,
      655,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      656,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 657,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 658,  -1,  -1,  -1,  -1, 659,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1, 660, 661,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 662,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 663,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 664,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 665,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1, 666,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 667,  -1,  -1,  -1,  -1,  -1,  -1, 668,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 669,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 670, 671,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      672,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      673,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 674,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 675,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 676,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1, 677,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 678,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 679,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      680,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      681,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      682,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      683,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      684,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 685,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 686,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1, 687,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1, 688,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 689,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
      690,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1, 691,  -1,  -1,  -1,  -1,
       -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
       -1,  -1,  -1, 692
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = property_hash_function (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          int index = lookup[key];

          if (index >= 0)
            {
              const char *s = property_word_list[index].name_offset + CSSPropStringPool;

              if (*str == *s && !strncmp (str + 1, s + 1, len - 1) && s[len] == '\0')
                return &property_word_list[index];
            }
        }
    }
  return 0;
}


const Property* FindProperty(const char* str, unsigned int len) {
  return CSSPropertyNamesHash::findPropertyImpl(str, len);
}

CSSPropertyID CssPropertyID(const ExecutionContext* execution_context,
                            const String& string)
{
    return ResolveCSSPropertyID(UnresolvedCSSPropertyID(execution_context,
                                                        string));
}

//mojom::blink::CSSSampleId GetCSSSampleId(CSSPropertyID id) {
//  switch (id) {
//    case CSSPropertyID::kColorScheme:
//      return mojom::blink::CSSSampleId::kColorScheme;
//    case CSSPropertyID::kForcedColorAdjust:
//      return mojom::blink::CSSSampleId::kForcedColorAdjust;
//    case CSSPropertyID::kMaskImage:
//      return mojom::blink::CSSSampleId::kMaskImage;
//    case CSSPropertyID::kMathDepth:
//      return mojom::blink::CSSSampleId::kMathDepth;
//    case CSSPropertyID::kPosition:
//      return mojom::blink::CSSSampleId::kPosition;
//    case CSSPropertyID::kPositionAnchor:
//      return mojom::blink::CSSSampleId::kPositionAnchor;
//    case CSSPropertyID::kAppearance:
//      return mojom::blink::CSSSampleId::kAppearance;
//    case CSSPropertyID::kColor:
//      return mojom::blink::CSSSampleId::kColor;
//    case CSSPropertyID::kDirection:
//      return mojom::blink::CSSSampleId::kDirection;
//    case CSSPropertyID::kFontFamily:
//      return mojom::blink::CSSSampleId::kFontFamily;
//    case CSSPropertyID::kFontFeatureSettings:
//      return mojom::blink::CSSSampleId::kFontFeatureSettings;
//    case CSSPropertyID::kFontKerning:
//      return mojom::blink::CSSSampleId::kFontKerning;
//    case CSSPropertyID::kFontOpticalSizing:
//      return mojom::blink::CSSSampleId::kFontOpticalSizing;
//    case CSSPropertyID::kFontPalette:
//      return mojom::blink::CSSSampleId::kFontPalette;
//    case CSSPropertyID::kFontSize:
//      return mojom::blink::CSSSampleId::kFontSize;
//    case CSSPropertyID::kFontSizeAdjust:
//      return mojom::blink::CSSSampleId::kFontSizeAdjust;
//    case CSSPropertyID::kFontStretch:
//      return mojom::blink::CSSSampleId::kFontStretch;
//    case CSSPropertyID::kFontStyle:
//      return mojom::blink::CSSSampleId::kFontStyle;
//    case CSSPropertyID::kFontSynthesisSmallCaps:
//      return mojom::blink::CSSSampleId::kFontSynthesisSmallCaps;
//    case CSSPropertyID::kFontSynthesisStyle:
//      return mojom::blink::CSSSampleId::kFontSynthesisStyle;
//    case CSSPropertyID::kFontSynthesisWeight:
//      return mojom::blink::CSSSampleId::kFontSynthesisWeight;
//    case CSSPropertyID::kFontVariantAlternates:
//      return mojom::blink::CSSSampleId::kFontVariantAlternates;
//    case CSSPropertyID::kFontVariantCaps:
//      return mojom::blink::CSSSampleId::kFontVariantCaps;
//    case CSSPropertyID::kFontVariantEastAsian:
//      return mojom::blink::CSSSampleId::kFontVariantEastAsian;
//    case CSSPropertyID::kFontVariantEmoji:
//      return mojom::blink::CSSSampleId::kFontVariantEmoji;
//    case CSSPropertyID::kFontVariantLigatures:
//      return mojom::blink::CSSSampleId::kFontVariantLigatures;
//    case CSSPropertyID::kFontVariantNumeric:
//      return mojom::blink::CSSSampleId::kFontVariantNumeric;
//    case CSSPropertyID::kFontVariantPosition:
//      return mojom::blink::CSSSampleId::kFontVariantPosition;
//    case CSSPropertyID::kFontVariationSettings:
//      return mojom::blink::CSSSampleId::kFontVariationSettings;
//    case CSSPropertyID::kFontWeight:
//      return mojom::blink::CSSSampleId::kFontWeight;
//    case CSSPropertyID::kInsetArea:
//      return mojom::blink::CSSSampleId::kInsetArea;
//    case CSSPropertyID::kInternalVisitedColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedColor;
//    case CSSPropertyID::kTextOrientation:
//      return mojom::blink::CSSSampleId::kTextOrientation;
//    case CSSPropertyID::kTextRendering:
//      return mojom::blink::CSSSampleId::kTextRendering;
//    case CSSPropertyID::kTextSpacingTrim:
//      return mojom::blink::CSSSampleId::kTextSpacingTrim;
//    case CSSPropertyID::kWebkitFontSmoothing:
//      return mojom::blink::CSSSampleId::kWebkitFontSmoothing;
//    case CSSPropertyID::kWebkitLocale:
//      return mojom::blink::CSSSampleId::kWebkitLocale;
//    case CSSPropertyID::kWebkitTextOrientation:
//      return mojom::blink::CSSSampleId::kWebkitTextOrientation;
//    case CSSPropertyID::kWebkitWritingMode:
//      return mojom::blink::CSSSampleId::kWebkitWritingMode;
//    case CSSPropertyID::kWritingMode:
//      return mojom::blink::CSSSampleId::kWritingMode;
//    case CSSPropertyID::kZoom:
//      return mojom::blink::CSSSampleId::kZoom;
//    case CSSPropertyID::kAccentColor:
//      return mojom::blink::CSSSampleId::kAccentColor;
//    case CSSPropertyID::kAdditiveSymbols:
//      return mojom::blink::CSSSampleId::kAdditiveSymbols;
//    case CSSPropertyID::kAlignContent:
//      return mojom::blink::CSSSampleId::kAlignContent;
//    case CSSPropertyID::kAlignItems:
//      return mojom::blink::CSSSampleId::kAlignItems;
//    case CSSPropertyID::kAlignSelf:
//      return mojom::blink::CSSSampleId::kAlignSelf;
//    case CSSPropertyID::kAlignmentBaseline:
//      return mojom::blink::CSSSampleId::kAlignmentBaseline;
//    case CSSPropertyID::kAll:
//      return mojom::blink::CSSSampleId::kAll;
//    case CSSPropertyID::kAnchorName:
//      return mojom::blink::CSSSampleId::kAnchorName;
//    case CSSPropertyID::kAnimationComposition:
//      return mojom::blink::CSSSampleId::kAnimationComposition;
//    case CSSPropertyID::kAnimationDelay:
//      return mojom::blink::CSSSampleId::kAnimationDelay;
//    case CSSPropertyID::kAnimationDirection:
//      return mojom::blink::CSSSampleId::kAnimationDirection;
//    case CSSPropertyID::kAnimationDuration:
//      return mojom::blink::CSSSampleId::kAnimationDuration;
//    case CSSPropertyID::kAnimationFillMode:
//      return mojom::blink::CSSSampleId::kAnimationFillMode;
//    case CSSPropertyID::kAnimationIterationCount:
//      return mojom::blink::CSSSampleId::kAnimationIterationCount;
//    case CSSPropertyID::kAnimationName:
//      return mojom::blink::CSSSampleId::kAnimationName;
//    case CSSPropertyID::kAnimationPlayState:
//      return mojom::blink::CSSSampleId::kAnimationPlayState;
//    case CSSPropertyID::kAnimationRangeEnd:
//      return mojom::blink::CSSSampleId::kAnimationRangeEnd;
//    case CSSPropertyID::kAnimationRangeStart:
//      return mojom::blink::CSSSampleId::kAnimationRangeStart;
//    case CSSPropertyID::kAnimationTimeline:
//      return mojom::blink::CSSSampleId::kAnimationTimeline;
//    case CSSPropertyID::kAnimationTimingFunction:
//      return mojom::blink::CSSSampleId::kAnimationTimingFunction;
//    case CSSPropertyID::kAppRegion:
//      return mojom::blink::CSSSampleId::kAppRegion;
//    case CSSPropertyID::kAscentOverride:
//      return mojom::blink::CSSSampleId::kAscentOverride;
//    case CSSPropertyID::kAspectRatio:
//      return mojom::blink::CSSSampleId::kAspectRatio;
//    case CSSPropertyID::kBackdropFilter:
//      return mojom::blink::CSSSampleId::kBackdropFilter;
//    case CSSPropertyID::kBackfaceVisibility:
//      return mojom::blink::CSSSampleId::kBackfaceVisibility;
//    case CSSPropertyID::kBackgroundAttachment:
//      return mojom::blink::CSSSampleId::kBackgroundAttachment;
//    case CSSPropertyID::kBackgroundBlendMode:
//      return mojom::blink::CSSSampleId::kBackgroundBlendMode;
//    case CSSPropertyID::kBackgroundClip:
//      return mojom::blink::CSSSampleId::kBackgroundClip;
//    case CSSPropertyID::kBackgroundColor:
//      return mojom::blink::CSSSampleId::kBackgroundColor;
//    case CSSPropertyID::kBackgroundImage:
//      return mojom::blink::CSSSampleId::kBackgroundImage;
//    case CSSPropertyID::kBackgroundOrigin:
//      return mojom::blink::CSSSampleId::kBackgroundOrigin;
//    case CSSPropertyID::kBackgroundPositionX:
//      return mojom::blink::CSSSampleId::kBackgroundPositionX;
//    case CSSPropertyID::kBackgroundPositionY:
//      return mojom::blink::CSSSampleId::kBackgroundPositionY;
//    case CSSPropertyID::kBackgroundRepeat:
//      return mojom::blink::CSSSampleId::kBackgroundRepeat;
//    case CSSPropertyID::kBackgroundSize:
//      return mojom::blink::CSSSampleId::kBackgroundSize;
//    case CSSPropertyID::kBasePalette:
//      return mojom::blink::CSSSampleId::kBasePalette;
//    case CSSPropertyID::kBaselineShift:
//      return mojom::blink::CSSSampleId::kBaselineShift;
//    case CSSPropertyID::kBaselineSource:
//      return mojom::blink::CSSSampleId::kBaselineSource;
//    case CSSPropertyID::kBlockSize:
//      return mojom::blink::CSSSampleId::kBlockSize;
//    case CSSPropertyID::kBorderBlockEndColor:
//      return mojom::blink::CSSSampleId::kBorderBlockEndColor;
//    case CSSPropertyID::kBorderBlockEndStyle:
//      return mojom::blink::CSSSampleId::kBorderBlockEndStyle;
//    case CSSPropertyID::kBorderBlockEndWidth:
//      return mojom::blink::CSSSampleId::kBorderBlockEndWidth;
//    case CSSPropertyID::kBorderBlockStartColor:
//      return mojom::blink::CSSSampleId::kBorderBlockStartColor;
//    case CSSPropertyID::kBorderBlockStartStyle:
//      return mojom::blink::CSSSampleId::kBorderBlockStartStyle;
//    case CSSPropertyID::kBorderBlockStartWidth:
//      return mojom::blink::CSSSampleId::kBorderBlockStartWidth;
//    case CSSPropertyID::kBorderBottomColor:
//      return mojom::blink::CSSSampleId::kBorderBottomColor;
//    case CSSPropertyID::kBorderBottomLeftRadius:
//      return mojom::blink::CSSSampleId::kBorderBottomLeftRadius;
//    case CSSPropertyID::kBorderBottomRightRadius:
//      return mojom::blink::CSSSampleId::kBorderBottomRightRadius;
//    case CSSPropertyID::kBorderBottomStyle:
//      return mojom::blink::CSSSampleId::kBorderBottomStyle;
//    case CSSPropertyID::kBorderBottomWidth:
//      return mojom::blink::CSSSampleId::kBorderBottomWidth;
//    case CSSPropertyID::kBorderCollapse:
//      return mojom::blink::CSSSampleId::kBorderCollapse;
//    case CSSPropertyID::kBorderEndEndRadius:
//      return mojom::blink::CSSSampleId::kBorderEndEndRadius;
//    case CSSPropertyID::kBorderEndStartRadius:
//      return mojom::blink::CSSSampleId::kBorderEndStartRadius;
//    case CSSPropertyID::kBorderImageOutset:
//      return mojom::blink::CSSSampleId::kBorderImageOutset;
//    case CSSPropertyID::kBorderImageRepeat:
//      return mojom::blink::CSSSampleId::kBorderImageRepeat;
//    case CSSPropertyID::kBorderImageSlice:
//      return mojom::blink::CSSSampleId::kBorderImageSlice;
//    case CSSPropertyID::kBorderImageSource:
//      return mojom::blink::CSSSampleId::kBorderImageSource;
//    case CSSPropertyID::kBorderImageWidth:
//      return mojom::blink::CSSSampleId::kBorderImageWidth;
//    case CSSPropertyID::kBorderInlineEndColor:
//      return mojom::blink::CSSSampleId::kBorderInlineEndColor;
//    case CSSPropertyID::kBorderInlineEndStyle:
//      return mojom::blink::CSSSampleId::kBorderInlineEndStyle;
//    case CSSPropertyID::kBorderInlineEndWidth:
//      return mojom::blink::CSSSampleId::kBorderInlineEndWidth;
//    case CSSPropertyID::kBorderInlineStartColor:
//      return mojom::blink::CSSSampleId::kBorderInlineStartColor;
//    case CSSPropertyID::kBorderInlineStartStyle:
//      return mojom::blink::CSSSampleId::kBorderInlineStartStyle;
//    case CSSPropertyID::kBorderInlineStartWidth:
//      return mojom::blink::CSSSampleId::kBorderInlineStartWidth;
//    case CSSPropertyID::kBorderLeftColor:
//      return mojom::blink::CSSSampleId::kBorderLeftColor;
//    case CSSPropertyID::kBorderLeftStyle:
//      return mojom::blink::CSSSampleId::kBorderLeftStyle;
//    case CSSPropertyID::kBorderLeftWidth:
//      return mojom::blink::CSSSampleId::kBorderLeftWidth;
//    case CSSPropertyID::kBorderRightColor:
//      return mojom::blink::CSSSampleId::kBorderRightColor;
//    case CSSPropertyID::kBorderRightStyle:
//      return mojom::blink::CSSSampleId::kBorderRightStyle;
//    case CSSPropertyID::kBorderRightWidth:
//      return mojom::blink::CSSSampleId::kBorderRightWidth;
//    case CSSPropertyID::kBorderStartEndRadius:
//      return mojom::blink::CSSSampleId::kBorderStartEndRadius;
//    case CSSPropertyID::kBorderStartStartRadius:
//      return mojom::blink::CSSSampleId::kBorderStartStartRadius;
//    case CSSPropertyID::kBorderTopColor:
//      return mojom::blink::CSSSampleId::kBorderTopColor;
//    case CSSPropertyID::kBorderTopLeftRadius:
//      return mojom::blink::CSSSampleId::kBorderTopLeftRadius;
//    case CSSPropertyID::kBorderTopRightRadius:
//      return mojom::blink::CSSSampleId::kBorderTopRightRadius;
//    case CSSPropertyID::kBorderTopStyle:
//      return mojom::blink::CSSSampleId::kBorderTopStyle;
//    case CSSPropertyID::kBorderTopWidth:
//      return mojom::blink::CSSSampleId::kBorderTopWidth;
//    case CSSPropertyID::kBottom:
//      return mojom::blink::CSSSampleId::kBottom;
//    case CSSPropertyID::kBoxShadow:
//      return mojom::blink::CSSSampleId::kBoxShadow;
//    case CSSPropertyID::kBoxSizing:
//      return mojom::blink::CSSSampleId::kBoxSizing;
//    case CSSPropertyID::kBreakAfter:
//      return mojom::blink::CSSSampleId::kBreakAfter;
//    case CSSPropertyID::kBreakBefore:
//      return mojom::blink::CSSSampleId::kBreakBefore;
//    case CSSPropertyID::kBreakInside:
//      return mojom::blink::CSSSampleId::kBreakInside;
//    case CSSPropertyID::kBufferedRendering:
//      return mojom::blink::CSSSampleId::kBufferedRendering;
//    case CSSPropertyID::kCaptionSide:
//      return mojom::blink::CSSSampleId::kCaptionSide;
//    case CSSPropertyID::kCaretColor:
//      return mojom::blink::CSSSampleId::kCaretColor;
//    case CSSPropertyID::kClear:
//      return mojom::blink::CSSSampleId::kClear;
//    case CSSPropertyID::kClip:
//      return mojom::blink::CSSSampleId::kClip;
//    case CSSPropertyID::kClipPath:
//      return mojom::blink::CSSSampleId::kClipPath;
//    case CSSPropertyID::kClipRule:
//      return mojom::blink::CSSSampleId::kClipRule;
//    case CSSPropertyID::kColorInterpolation:
//      return mojom::blink::CSSSampleId::kColorInterpolation;
//    case CSSPropertyID::kColorInterpolationFilters:
//      return mojom::blink::CSSSampleId::kColorInterpolationFilters;
//    case CSSPropertyID::kColorRendering:
//      return mojom::blink::CSSSampleId::kColorRendering;
//    case CSSPropertyID::kColumnCount:
//      return mojom::blink::CSSSampleId::kColumnCount;
//    case CSSPropertyID::kColumnFill:
//      return mojom::blink::CSSSampleId::kColumnFill;
//    case CSSPropertyID::kColumnGap:
//      return mojom::blink::CSSSampleId::kColumnGap;
//    case CSSPropertyID::kColumnRuleColor:
//      return mojom::blink::CSSSampleId::kColumnRuleColor;
//    case CSSPropertyID::kColumnRuleStyle:
//      return mojom::blink::CSSSampleId::kColumnRuleStyle;
//    case CSSPropertyID::kColumnRuleWidth:
//      return mojom::blink::CSSSampleId::kColumnRuleWidth;
//    case CSSPropertyID::kColumnSpan:
//      return mojom::blink::CSSSampleId::kColumnSpan;
//    case CSSPropertyID::kColumnWidth:
//      return mojom::blink::CSSSampleId::kColumnWidth;
//    case CSSPropertyID::kContain:
//      return mojom::blink::CSSSampleId::kContain;
//    case CSSPropertyID::kContainIntrinsicBlockSize:
//      return mojom::blink::CSSSampleId::kContainIntrinsicBlockSize;
//    case CSSPropertyID::kContainIntrinsicHeight:
//      return mojom::blink::CSSSampleId::kContainIntrinsicHeight;
//    case CSSPropertyID::kContainIntrinsicInlineSize:
//      return mojom::blink::CSSSampleId::kContainIntrinsicInlineSize;
//    case CSSPropertyID::kContainIntrinsicWidth:
//      return mojom::blink::CSSSampleId::kContainIntrinsicWidth;
//    case CSSPropertyID::kContainerName:
//      return mojom::blink::CSSSampleId::kContainerName;
//    case CSSPropertyID::kContainerType:
//      return mojom::blink::CSSSampleId::kContainerType;
//    case CSSPropertyID::kContent:
//      return mojom::blink::CSSSampleId::kContent;
//    case CSSPropertyID::kContentVisibility:
//      return mojom::blink::CSSSampleId::kContentVisibility;
//    case CSSPropertyID::kCounterIncrement:
//      return mojom::blink::CSSSampleId::kCounterIncrement;
//    case CSSPropertyID::kCounterReset:
//      return mojom::blink::CSSSampleId::kCounterReset;
//    case CSSPropertyID::kCounterSet:
//      return mojom::blink::CSSSampleId::kCounterSet;
//    case CSSPropertyID::kCursor:
//      return mojom::blink::CSSSampleId::kCursor;
//    case CSSPropertyID::kCx:
//      return mojom::blink::CSSSampleId::kCx;
//    case CSSPropertyID::kCy:
//      return mojom::blink::CSSSampleId::kCy;
//    case CSSPropertyID::kD:
//      return mojom::blink::CSSSampleId::kD;
//    case CSSPropertyID::kDescentOverride:
//      return mojom::blink::CSSSampleId::kDescentOverride;
//    case CSSPropertyID::kDisplay:
//      return mojom::blink::CSSSampleId::kDisplay;
//    case CSSPropertyID::kDominantBaseline:
//      return mojom::blink::CSSSampleId::kDominantBaseline;
//    case CSSPropertyID::kDynamicRangeLimit:
//      return mojom::blink::CSSSampleId::kDynamicRangeLimit;
//    case CSSPropertyID::kEmptyCells:
//      return mojom::blink::CSSSampleId::kEmptyCells;
//    case CSSPropertyID::kFallback:
//      return mojom::blink::CSSSampleId::kFallback;
//    case CSSPropertyID::kFieldSizing:
//      return mojom::blink::CSSSampleId::kFieldSizing;
//    case CSSPropertyID::kFill:
//      return mojom::blink::CSSSampleId::kFill;
//    case CSSPropertyID::kFillOpacity:
//      return mojom::blink::CSSSampleId::kFillOpacity;
//    case CSSPropertyID::kFillRule:
//      return mojom::blink::CSSSampleId::kFillRule;
//    case CSSPropertyID::kFilter:
//      return mojom::blink::CSSSampleId::kFilter;
//    case CSSPropertyID::kFlexBasis:
//      return mojom::blink::CSSSampleId::kFlexBasis;
//    case CSSPropertyID::kFlexDirection:
//      return mojom::blink::CSSSampleId::kFlexDirection;
//    case CSSPropertyID::kFlexGrow:
//      return mojom::blink::CSSSampleId::kFlexGrow;
//    case CSSPropertyID::kFlexShrink:
//      return mojom::blink::CSSSampleId::kFlexShrink;
//    case CSSPropertyID::kFlexWrap:
//      return mojom::blink::CSSSampleId::kFlexWrap;
//    case CSSPropertyID::kFloat:
//      return mojom::blink::CSSSampleId::kFloat;
//    case CSSPropertyID::kFloodColor:
//      return mojom::blink::CSSSampleId::kFloodColor;
//    case CSSPropertyID::kFloodOpacity:
//      return mojom::blink::CSSSampleId::kFloodOpacity;
//    case CSSPropertyID::kFontDisplay:
//      return mojom::blink::CSSSampleId::kFontDisplay;
//    case CSSPropertyID::kGridAutoColumns:
//      return mojom::blink::CSSSampleId::kGridAutoColumns;
//    case CSSPropertyID::kGridAutoFlow:
//      return mojom::blink::CSSSampleId::kGridAutoFlow;
//    case CSSPropertyID::kGridAutoRows:
//      return mojom::blink::CSSSampleId::kGridAutoRows;
//    case CSSPropertyID::kGridColumnEnd:
//      return mojom::blink::CSSSampleId::kGridColumnEnd;
//    case CSSPropertyID::kGridColumnStart:
//      return mojom::blink::CSSSampleId::kGridColumnStart;
//    case CSSPropertyID::kGridRowEnd:
//      return mojom::blink::CSSSampleId::kGridRowEnd;
//    case CSSPropertyID::kGridRowStart:
//      return mojom::blink::CSSSampleId::kGridRowStart;
//    case CSSPropertyID::kGridTemplateAreas:
//      return mojom::blink::CSSSampleId::kGridTemplateAreas;
//    case CSSPropertyID::kGridTemplateColumns:
//      return mojom::blink::CSSSampleId::kGridTemplateColumns;
//    case CSSPropertyID::kGridTemplateRows:
//      return mojom::blink::CSSSampleId::kGridTemplateRows;
//    case CSSPropertyID::kHeight:
//      return mojom::blink::CSSSampleId::kHeight;
//    case CSSPropertyID::kHyphenateCharacter:
//      return mojom::blink::CSSSampleId::kHyphenateCharacter;
//    case CSSPropertyID::kHyphenateLimitChars:
//      return mojom::blink::CSSSampleId::kHyphenateLimitChars;
//    case CSSPropertyID::kHyphens:
//      return mojom::blink::CSSSampleId::kHyphens;
//    case CSSPropertyID::kImageOrientation:
//      return mojom::blink::CSSSampleId::kImageOrientation;
//    case CSSPropertyID::kImageRendering:
//      return mojom::blink::CSSSampleId::kImageRendering;
//    case CSSPropertyID::kInherits:
//      return mojom::blink::CSSSampleId::kInherits;
//    case CSSPropertyID::kInitialLetter:
//      return mojom::blink::CSSSampleId::kInitialLetter;
//    case CSSPropertyID::kInitialValue:
//      return mojom::blink::CSSSampleId::kInitialValue;
//    case CSSPropertyID::kInlineSize:
//      return mojom::blink::CSSSampleId::kInlineSize;
//    case CSSPropertyID::kInsetBlockEnd:
//      return mojom::blink::CSSSampleId::kInsetBlockEnd;
//    case CSSPropertyID::kInsetBlockStart:
//      return mojom::blink::CSSSampleId::kInsetBlockStart;
//    case CSSPropertyID::kInsetInlineEnd:
//      return mojom::blink::CSSSampleId::kInsetInlineEnd;
//    case CSSPropertyID::kInsetInlineStart:
//      return mojom::blink::CSSSampleId::kInsetInlineStart;
//    case CSSPropertyID::kInternalAlignContentBlock:
//      return mojom::blink::CSSSampleId::kInternalAlignContentBlock;
//    case CSSPropertyID::kInternalEmptyLineHeight:
//      return mojom::blink::CSSSampleId::kInternalEmptyLineHeight;
//    case CSSPropertyID::kInternalFontSizeDelta:
//      return mojom::blink::CSSSampleId::kInternalFontSizeDelta;
//    case CSSPropertyID::kInternalForcedBackgroundColor:
//      return mojom::blink::CSSSampleId::kInternalForcedBackgroundColor;
//    case CSSPropertyID::kInternalForcedBorderColor:
//      return mojom::blink::CSSSampleId::kInternalForcedBorderColor;
//    case CSSPropertyID::kInternalForcedColor:
//      return mojom::blink::CSSSampleId::kInternalForcedColor;
//    case CSSPropertyID::kInternalForcedOutlineColor:
//      return mojom::blink::CSSSampleId::kInternalForcedOutlineColor;
//    case CSSPropertyID::kInternalForcedVisitedColor:
//      return mojom::blink::CSSSampleId::kInternalForcedVisitedColor;
//    case CSSPropertyID::kInternalOverflowBlock:
//      return mojom::blink::CSSSampleId::kInternalOverflowBlock;
//    case CSSPropertyID::kInternalOverflowInline:
//      return mojom::blink::CSSSampleId::kInternalOverflowInline;
//    case CSSPropertyID::kInternalVisitedBackgroundColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBackgroundColor;
//    case CSSPropertyID::kInternalVisitedBorderBlockEndColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderBlockEndColor;
//    case CSSPropertyID::kInternalVisitedBorderBlockStartColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderBlockStartColor;
//    case CSSPropertyID::kInternalVisitedBorderBottomColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderBottomColor;
//    case CSSPropertyID::kInternalVisitedBorderInlineEndColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderInlineEndColor;
//    case CSSPropertyID::kInternalVisitedBorderInlineStartColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderInlineStartColor;
//    case CSSPropertyID::kInternalVisitedBorderLeftColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderLeftColor;
//    case CSSPropertyID::kInternalVisitedBorderRightColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderRightColor;
//    case CSSPropertyID::kInternalVisitedBorderTopColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedBorderTopColor;
//    case CSSPropertyID::kInternalVisitedCaretColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedCaretColor;
//    case CSSPropertyID::kInternalVisitedColumnRuleColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedColumnRuleColor;
//    case CSSPropertyID::kInternalVisitedFill:
//      return mojom::blink::CSSSampleId::kInternalVisitedFill;
//    case CSSPropertyID::kInternalVisitedOutlineColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedOutlineColor;
//    case CSSPropertyID::kInternalVisitedStroke:
//      return mojom::blink::CSSSampleId::kInternalVisitedStroke;
//    case CSSPropertyID::kInternalVisitedTextDecorationColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedTextDecorationColor;
//    case CSSPropertyID::kInternalVisitedTextEmphasisColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedTextEmphasisColor;
//    case CSSPropertyID::kInternalVisitedTextFillColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedTextFillColor;
//    case CSSPropertyID::kInternalVisitedTextStrokeColor:
//      return mojom::blink::CSSSampleId::kInternalVisitedTextStrokeColor;
//    case CSSPropertyID::kIsolation:
//      return mojom::blink::CSSSampleId::kIsolation;
//    case CSSPropertyID::kJustifyContent:
//      return mojom::blink::CSSSampleId::kJustifyContent;
//    case CSSPropertyID::kJustifyItems:
//      return mojom::blink::CSSSampleId::kJustifyItems;
//    case CSSPropertyID::kJustifySelf:
//      return mojom::blink::CSSSampleId::kJustifySelf;
//    case CSSPropertyID::kLeft:
//      return mojom::blink::CSSSampleId::kLeft;
//    case CSSPropertyID::kLetterSpacing:
//      return mojom::blink::CSSSampleId::kLetterSpacing;
//    case CSSPropertyID::kLightingColor:
//      return mojom::blink::CSSSampleId::kLightingColor;
//    case CSSPropertyID::kLineBreak:
//      return mojom::blink::CSSSampleId::kLineBreak;
//    case CSSPropertyID::kLineClamp:
//      return mojom::blink::CSSSampleId::kLineClamp;
//    case CSSPropertyID::kLineGapOverride:
//      return mojom::blink::CSSSampleId::kLineGapOverride;
//    case CSSPropertyID::kLineHeight:
//      return mojom::blink::CSSSampleId::kLineHeight;
//    case CSSPropertyID::kListStyleImage:
//      return mojom::blink::CSSSampleId::kListStyleImage;
//    case CSSPropertyID::kListStylePosition:
//      return mojom::blink::CSSSampleId::kListStylePosition;
//    case CSSPropertyID::kListStyleType:
//      return mojom::blink::CSSSampleId::kListStyleType;
//    case CSSPropertyID::kMarginBlockEnd:
//      return mojom::blink::CSSSampleId::kMarginBlockEnd;
//    case CSSPropertyID::kMarginBlockStart:
//      return mojom::blink::CSSSampleId::kMarginBlockStart;
//    case CSSPropertyID::kMarginBottom:
//      return mojom::blink::CSSSampleId::kMarginBottom;
//    case CSSPropertyID::kMarginInlineEnd:
//      return mojom::blink::CSSSampleId::kMarginInlineEnd;
//    case CSSPropertyID::kMarginInlineStart:
//      return mojom::blink::CSSSampleId::kMarginInlineStart;
//    case CSSPropertyID::kMarginLeft:
//      return mojom::blink::CSSSampleId::kMarginLeft;
//    case CSSPropertyID::kMarginRight:
//      return mojom::blink::CSSSampleId::kMarginRight;
//    case CSSPropertyID::kMarginTop:
//      return mojom::blink::CSSSampleId::kMarginTop;
//    case CSSPropertyID::kMarkerEnd:
//      return mojom::blink::CSSSampleId::kMarkerEnd;
//    case CSSPropertyID::kMarkerMid:
//      return mojom::blink::CSSSampleId::kMarkerMid;
//    case CSSPropertyID::kMarkerStart:
//      return mojom::blink::CSSSampleId::kMarkerStart;
//    case CSSPropertyID::kMaskClip:
//      return mojom::blink::CSSSampleId::kMaskClip;
//    case CSSPropertyID::kMaskComposite:
//      return mojom::blink::CSSSampleId::kMaskComposite;
//    case CSSPropertyID::kMaskMode:
//      return mojom::blink::CSSSampleId::kMaskMode;
//    case CSSPropertyID::kMaskOrigin:
//      return mojom::blink::CSSSampleId::kMaskOrigin;
//    case CSSPropertyID::kMaskRepeat:
//      return mojom::blink::CSSSampleId::kMaskRepeat;
//    case CSSPropertyID::kMaskSize:
//      return mojom::blink::CSSSampleId::kMaskSize;
//    case CSSPropertyID::kMaskType:
//      return mojom::blink::CSSSampleId::kMaskType;
//    case CSSPropertyID::kMathShift:
//      return mojom::blink::CSSSampleId::kMathShift;
//    case CSSPropertyID::kMathStyle:
//      return mojom::blink::CSSSampleId::kMathStyle;
//    case CSSPropertyID::kMaxBlockSize:
//      return mojom::blink::CSSSampleId::kMaxBlockSize;
//    case CSSPropertyID::kMaxHeight:
//      return mojom::blink::CSSSampleId::kMaxHeight;
//    case CSSPropertyID::kMaxInlineSize:
//      return mojom::blink::CSSSampleId::kMaxInlineSize;
//    case CSSPropertyID::kMaxWidth:
//      return mojom::blink::CSSSampleId::kMaxWidth;
//    case CSSPropertyID::kMinBlockSize:
//      return mojom::blink::CSSSampleId::kMinBlockSize;
//    case CSSPropertyID::kMinHeight:
//      return mojom::blink::CSSSampleId::kMinHeight;
//    case CSSPropertyID::kMinInlineSize:
//      return mojom::blink::CSSSampleId::kMinInlineSize;
//    case CSSPropertyID::kMinWidth:
//      return mojom::blink::CSSSampleId::kMinWidth;
//    case CSSPropertyID::kMixBlendMode:
//      return mojom::blink::CSSSampleId::kMixBlendMode;
//    case CSSPropertyID::kNavigation:
//      return mojom::blink::CSSSampleId::kNavigation;
//    case CSSPropertyID::kNegative:
//      return mojom::blink::CSSSampleId::kNegative;
//    case CSSPropertyID::kObjectFit:
//      return mojom::blink::CSSSampleId::kObjectFit;
//    case CSSPropertyID::kObjectPosition:
//      return mojom::blink::CSSSampleId::kObjectPosition;
//    case CSSPropertyID::kObjectViewBox:
//      return mojom::blink::CSSSampleId::kObjectViewBox;
//    case CSSPropertyID::kOffsetAnchor:
//      return mojom::blink::CSSSampleId::kOffsetAnchor;
//    case CSSPropertyID::kOffsetDistance:
//      return mojom::blink::CSSSampleId::kOffsetDistance;
//    case CSSPropertyID::kOffsetPath:
//      return mojom::blink::CSSSampleId::kOffsetPath;
//    case CSSPropertyID::kOffsetPosition:
//      return mojom::blink::CSSSampleId::kOffsetPosition;
//    case CSSPropertyID::kOffsetRotate:
//      return mojom::blink::CSSSampleId::kOffsetRotate;
//    case CSSPropertyID::kOpacity:
//      return mojom::blink::CSSSampleId::kOpacity;
//    case CSSPropertyID::kOrder:
//      return mojom::blink::CSSSampleId::kOrder;
//    case CSSPropertyID::kOriginTrialTestProperty:
//      return mojom::blink::CSSSampleId::kOriginTrialTestProperty;
//    case CSSPropertyID::kOrphans:
//      return mojom::blink::CSSSampleId::kOrphans;
//    case CSSPropertyID::kOutlineColor:
//      return mojom::blink::CSSSampleId::kOutlineColor;
//    case CSSPropertyID::kOutlineOffset:
//      return mojom::blink::CSSSampleId::kOutlineOffset;
//    case CSSPropertyID::kOutlineStyle:
//      return mojom::blink::CSSSampleId::kOutlineStyle;
//    case CSSPropertyID::kOutlineWidth:
//      return mojom::blink::CSSSampleId::kOutlineWidth;
//    case CSSPropertyID::kOverflowAnchor:
//      return mojom::blink::CSSSampleId::kOverflowAnchor;
//    case CSSPropertyID::kOverflowBlock:
//      return mojom::blink::CSSSampleId::kOverflowBlock;
//    case CSSPropertyID::kOverflowClipMargin:
//      return mojom::blink::CSSSampleId::kOverflowClipMargin;
//    case CSSPropertyID::kOverflowInline:
//      return mojom::blink::CSSSampleId::kOverflowInline;
//    case CSSPropertyID::kOverflowWrap:
//      return mojom::blink::CSSSampleId::kOverflowWrap;
//    case CSSPropertyID::kOverflowX:
//      return mojom::blink::CSSSampleId::kOverflowX;
//    case CSSPropertyID::kOverflowY:
//      return mojom::blink::CSSSampleId::kOverflowY;
//    case CSSPropertyID::kOverlay:
//      return mojom::blink::CSSSampleId::kOverlay;
//    case CSSPropertyID::kOverrideColors:
//      return mojom::blink::CSSSampleId::kOverrideColors;
//    case CSSPropertyID::kOverscrollBehaviorBlock:
//      return mojom::blink::CSSSampleId::kOverscrollBehaviorBlock;
//    case CSSPropertyID::kOverscrollBehaviorInline:
//      return mojom::blink::CSSSampleId::kOverscrollBehaviorInline;
//    case CSSPropertyID::kOverscrollBehaviorX:
//      return mojom::blink::CSSSampleId::kOverscrollBehaviorX;
//    case CSSPropertyID::kOverscrollBehaviorY:
//      return mojom::blink::CSSSampleId::kOverscrollBehaviorY;
//    case CSSPropertyID::kPad:
//      return mojom::blink::CSSSampleId::kPad;
//    case CSSPropertyID::kPaddingBlockEnd:
//      return mojom::blink::CSSSampleId::kPaddingBlockEnd;
//    case CSSPropertyID::kPaddingBlockStart:
//      return mojom::blink::CSSSampleId::kPaddingBlockStart;
//    case CSSPropertyID::kPaddingBottom:
//      return mojom::blink::CSSSampleId::kPaddingBottom;
//    case CSSPropertyID::kPaddingInlineEnd:
//      return mojom::blink::CSSSampleId::kPaddingInlineEnd;
//    case CSSPropertyID::kPaddingInlineStart:
//      return mojom::blink::CSSSampleId::kPaddingInlineStart;
//    case CSSPropertyID::kPaddingLeft:
//      return mojom::blink::CSSSampleId::kPaddingLeft;
//    case CSSPropertyID::kPaddingRight:
//      return mojom::blink::CSSSampleId::kPaddingRight;
//    case CSSPropertyID::kPaddingTop:
//      return mojom::blink::CSSSampleId::kPaddingTop;
//    case CSSPropertyID::kPage:
//      return mojom::blink::CSSSampleId::kPage;
//    case CSSPropertyID::kPageOrientation:
//      return mojom::blink::CSSSampleId::kPageOrientation;
//    case CSSPropertyID::kPaintOrder:
//      return mojom::blink::CSSSampleId::kPaintOrder;
//    case CSSPropertyID::kPerspective:
//      return mojom::blink::CSSSampleId::kPerspective;
//    case CSSPropertyID::kPerspectiveOrigin:
//      return mojom::blink::CSSSampleId::kPerspectiveOrigin;
//    case CSSPropertyID::kPointerEvents:
//      return mojom::blink::CSSSampleId::kPointerEvents;
//    case CSSPropertyID::kPopoverHideDelay:
//      return mojom::blink::CSSSampleId::kPopoverHideDelay;
//    case CSSPropertyID::kPopoverShowDelay:
//      return mojom::blink::CSSSampleId::kPopoverShowDelay;
//    case CSSPropertyID::kPositionTryOptions:
//      return mojom::blink::CSSSampleId::kPositionTryOptions;
//    case CSSPropertyID::kPositionTryOrder:
//      return mojom::blink::CSSSampleId::kPositionTryOrder;
//    case CSSPropertyID::kPositionVisibility:
//      return mojom::blink::CSSSampleId::kPositionVisibility;
//    case CSSPropertyID::kPrefix:
//      return mojom::blink::CSSSampleId::kPrefix;
//    case CSSPropertyID::kQuotes:
//      return mojom::blink::CSSSampleId::kQuotes;
//    case CSSPropertyID::kR:
//      return mojom::blink::CSSSampleId::kR;
//    case CSSPropertyID::kRange:
//      return mojom::blink::CSSSampleId::kRange;
//    case CSSPropertyID::kReadingOrderItems:
//      return mojom::blink::CSSSampleId::kReadingOrderItems;
//    case CSSPropertyID::kResize:
//      return mojom::blink::CSSSampleId::kResize;
//    case CSSPropertyID::kRight:
//      return mojom::blink::CSSSampleId::kRight;
//    case CSSPropertyID::kRotate:
//      return mojom::blink::CSSSampleId::kRotate;
//    case CSSPropertyID::kRowGap:
//      return mojom::blink::CSSSampleId::kRowGap;
//    case CSSPropertyID::kRubyPosition:
//      return mojom::blink::CSSSampleId::kRubyPosition;
//    case CSSPropertyID::kRx:
//      return mojom::blink::CSSSampleId::kRx;
//    case CSSPropertyID::kRy:
//      return mojom::blink::CSSSampleId::kRy;
//    case CSSPropertyID::kScale:
//      return mojom::blink::CSSSampleId::kScale;
//    case CSSPropertyID::kScrollBehavior:
//      return mojom::blink::CSSSampleId::kScrollBehavior;
//    case CSSPropertyID::kScrollMarginBlockEnd:
//      return mojom::blink::CSSSampleId::kScrollMarginBlockEnd;
//    case CSSPropertyID::kScrollMarginBlockStart:
//      return mojom::blink::CSSSampleId::kScrollMarginBlockStart;
//    case CSSPropertyID::kScrollMarginBottom:
//      return mojom::blink::CSSSampleId::kScrollMarginBottom;
//    case CSSPropertyID::kScrollMarginInlineEnd:
//      return mojom::blink::CSSSampleId::kScrollMarginInlineEnd;
//    case CSSPropertyID::kScrollMarginInlineStart:
//      return mojom::blink::CSSSampleId::kScrollMarginInlineStart;
//    case CSSPropertyID::kScrollMarginLeft:
//      return mojom::blink::CSSSampleId::kScrollMarginLeft;
//    case CSSPropertyID::kScrollMarginRight:
//      return mojom::blink::CSSSampleId::kScrollMarginRight;
//    case CSSPropertyID::kScrollMarginTop:
//      return mojom::blink::CSSSampleId::kScrollMarginTop;
//    case CSSPropertyID::kScrollPaddingBlockEnd:
//      return mojom::blink::CSSSampleId::kScrollPaddingBlockEnd;
//    case CSSPropertyID::kScrollPaddingBlockStart:
//      return mojom::blink::CSSSampleId::kScrollPaddingBlockStart;
//    case CSSPropertyID::kScrollPaddingBottom:
//      return mojom::blink::CSSSampleId::kScrollPaddingBottom;
//    case CSSPropertyID::kScrollPaddingInlineEnd:
//      return mojom::blink::CSSSampleId::kScrollPaddingInlineEnd;
//    case CSSPropertyID::kScrollPaddingInlineStart:
//      return mojom::blink::CSSSampleId::kScrollPaddingInlineStart;
//    case CSSPropertyID::kScrollPaddingLeft:
//      return mojom::blink::CSSSampleId::kScrollPaddingLeft;
//    case CSSPropertyID::kScrollPaddingRight:
//      return mojom::blink::CSSSampleId::kScrollPaddingRight;
//    case CSSPropertyID::kScrollPaddingTop:
//      return mojom::blink::CSSSampleId::kScrollPaddingTop;
//    case CSSPropertyID::kScrollSnapAlign:
//      return mojom::blink::CSSSampleId::kScrollSnapAlign;
//    case CSSPropertyID::kScrollSnapStop:
//      return mojom::blink::CSSSampleId::kScrollSnapStop;
//    case CSSPropertyID::kScrollSnapType:
//      return mojom::blink::CSSSampleId::kScrollSnapType;
//    case CSSPropertyID::kScrollStartBlock:
//      return mojom::blink::CSSSampleId::kScrollStartBlock;
//    case CSSPropertyID::kScrollStartInline:
//      return mojom::blink::CSSSampleId::kScrollStartInline;
//    case CSSPropertyID::kScrollStartTargetBlock:
//      return mojom::blink::CSSSampleId::kScrollStartTargetBlock;
//    case CSSPropertyID::kScrollStartTargetInline:
//      return mojom::blink::CSSSampleId::kScrollStartTargetInline;
//    case CSSPropertyID::kScrollStartTargetX:
//      return mojom::blink::CSSSampleId::kScrollStartTargetX;
//    case CSSPropertyID::kScrollStartTargetY:
//      return mojom::blink::CSSSampleId::kScrollStartTargetY;
//    case CSSPropertyID::kScrollStartX:
//      return mojom::blink::CSSSampleId::kScrollStartX;
//    case CSSPropertyID::kScrollStartY:
//      return mojom::blink::CSSSampleId::kScrollStartY;
//    case CSSPropertyID::kScrollTimelineAxis:
//      return mojom::blink::CSSSampleId::kScrollTimelineAxis;
//    case CSSPropertyID::kScrollTimelineName:
//      return mojom::blink::CSSSampleId::kScrollTimelineName;
//    case CSSPropertyID::kScrollbarColor:
//      return mojom::blink::CSSSampleId::kScrollbarColor;
//    case CSSPropertyID::kScrollbarGutter:
//      return mojom::blink::CSSSampleId::kScrollbarGutter;
//    case CSSPropertyID::kScrollbarWidth:
//      return mojom::blink::CSSSampleId::kScrollbarWidth;
//    case CSSPropertyID::kShapeImageThreshold:
//      return mojom::blink::CSSSampleId::kShapeImageThreshold;
//    case CSSPropertyID::kShapeMargin:
//      return mojom::blink::CSSSampleId::kShapeMargin;
//    case CSSPropertyID::kShapeOutside:
//      return mojom::blink::CSSSampleId::kShapeOutside;
//    case CSSPropertyID::kShapeRendering:
//      return mojom::blink::CSSSampleId::kShapeRendering;
//    case CSSPropertyID::kSize:
//      return mojom::blink::CSSSampleId::kSize;
//    case CSSPropertyID::kSizeAdjust:
//      return mojom::blink::CSSSampleId::kSizeAdjust;
//    case CSSPropertyID::kSpeak:
//      return mojom::blink::CSSSampleId::kSpeak;
//    case CSSPropertyID::kSpeakAs:
//      return mojom::blink::CSSSampleId::kSpeakAs;
//    case CSSPropertyID::kSrc:
//      return mojom::blink::CSSSampleId::kSrc;
//    case CSSPropertyID::kStopColor:
//      return mojom::blink::CSSSampleId::kStopColor;
//    case CSSPropertyID::kStopOpacity:
//      return mojom::blink::CSSSampleId::kStopOpacity;
//    case CSSPropertyID::kStroke:
//      return mojom::blink::CSSSampleId::kStroke;
//    case CSSPropertyID::kStrokeDasharray:
//      return mojom::blink::CSSSampleId::kStrokeDasharray;
//    case CSSPropertyID::kStrokeDashoffset:
//      return mojom::blink::CSSSampleId::kStrokeDashoffset;
//    case CSSPropertyID::kStrokeLinecap:
//      return mojom::blink::CSSSampleId::kStrokeLinecap;
//    case CSSPropertyID::kStrokeLinejoin:
//      return mojom::blink::CSSSampleId::kStrokeLinejoin;
//    case CSSPropertyID::kStrokeMiterlimit:
//      return mojom::blink::CSSSampleId::kStrokeMiterlimit;
//    case CSSPropertyID::kStrokeOpacity:
//      return mojom::blink::CSSSampleId::kStrokeOpacity;
//    case CSSPropertyID::kStrokeWidth:
//      return mojom::blink::CSSSampleId::kStrokeWidth;
//    case CSSPropertyID::kSuffix:
//      return mojom::blink::CSSSampleId::kSuffix;
//    case CSSPropertyID::kSymbols:
//      return mojom::blink::CSSSampleId::kSymbols;
//    case CSSPropertyID::kSyntax:
//      return mojom::blink::CSSSampleId::kSyntax;
//    case CSSPropertyID::kSystem:
//      return mojom::blink::CSSSampleId::kSystem;
//    case CSSPropertyID::kTabSize:
//      return mojom::blink::CSSSampleId::kTabSize;
//    case CSSPropertyID::kTableLayout:
//      return mojom::blink::CSSSampleId::kTableLayout;
//    case CSSPropertyID::kTextAlign:
//      return mojom::blink::CSSSampleId::kTextAlign;
//    case CSSPropertyID::kTextAlignLast:
//      return mojom::blink::CSSSampleId::kTextAlignLast;
//    case CSSPropertyID::kTextAnchor:
//      return mojom::blink::CSSSampleId::kTextAnchor;
//    case CSSPropertyID::kTextAutospace:
//      return mojom::blink::CSSSampleId::kTextAutospace;
//    case CSSPropertyID::kTextBoxEdge:
//      return mojom::blink::CSSSampleId::kTextBoxEdge;
//    case CSSPropertyID::kTextBoxTrim:
//      return mojom::blink::CSSSampleId::kTextBoxTrim;
//    case CSSPropertyID::kTextCombineUpright:
//      return mojom::blink::CSSSampleId::kTextCombineUpright;
//    case CSSPropertyID::kTextDecorationColor:
//      return mojom::blink::CSSSampleId::kTextDecorationColor;
//    case CSSPropertyID::kTextDecorationLine:
//      return mojom::blink::CSSSampleId::kTextDecorationLine;
//    case CSSPropertyID::kTextDecorationSkipInk:
//      return mojom::blink::CSSSampleId::kTextDecorationSkipInk;
//    case CSSPropertyID::kTextDecorationStyle:
//      return mojom::blink::CSSSampleId::kTextDecorationStyle;
//    case CSSPropertyID::kTextDecorationThickness:
//      return mojom::blink::CSSSampleId::kTextDecorationThickness;
//    case CSSPropertyID::kTextEmphasisColor:
//      return mojom::blink::CSSSampleId::kTextEmphasisColor;
//    case CSSPropertyID::kTextEmphasisPosition:
//      return mojom::blink::CSSSampleId::kTextEmphasisPosition;
//    case CSSPropertyID::kTextEmphasisStyle:
//      return mojom::blink::CSSSampleId::kTextEmphasisStyle;
//    case CSSPropertyID::kTextIndent:
//      return mojom::blink::CSSSampleId::kTextIndent;
//    case CSSPropertyID::kTextOverflow:
//      return mojom::blink::CSSSampleId::kTextOverflow;
//    case CSSPropertyID::kTextShadow:
//      return mojom::blink::CSSSampleId::kTextShadow;
//    case CSSPropertyID::kTextSizeAdjust:
//      return mojom::blink::CSSSampleId::kTextSizeAdjust;
//    case CSSPropertyID::kTextTransform:
//      return mojom::blink::CSSSampleId::kTextTransform;
//    case CSSPropertyID::kTextUnderlineOffset:
//      return mojom::blink::CSSSampleId::kTextUnderlineOffset;
//    case CSSPropertyID::kTextUnderlinePosition:
//      return mojom::blink::CSSSampleId::kTextUnderlinePosition;
//    case CSSPropertyID::kTextWrap:
//      return mojom::blink::CSSSampleId::kTextWrap;
//    case CSSPropertyID::kTimelineScope:
//      return mojom::blink::CSSSampleId::kTimelineScope;
//    case CSSPropertyID::kTop:
//      return mojom::blink::CSSSampleId::kTop;
//    case CSSPropertyID::kTouchAction:
//      return mojom::blink::CSSSampleId::kTouchAction;
//    case CSSPropertyID::kTransform:
//      return mojom::blink::CSSSampleId::kTransform;
//    case CSSPropertyID::kTransformBox:
//      return mojom::blink::CSSSampleId::kTransformBox;
//    case CSSPropertyID::kTransformOrigin:
//      return mojom::blink::CSSSampleId::kTransformOrigin;
//    case CSSPropertyID::kTransformStyle:
//      return mojom::blink::CSSSampleId::kTransformStyle;
//    case CSSPropertyID::kTransitionBehavior:
//      return mojom::blink::CSSSampleId::kTransitionBehavior;
//    case CSSPropertyID::kTransitionDelay:
//      return mojom::blink::CSSSampleId::kTransitionDelay;
//    case CSSPropertyID::kTransitionDuration:
//      return mojom::blink::CSSSampleId::kTransitionDuration;
//    case CSSPropertyID::kTransitionProperty:
//      return mojom::blink::CSSSampleId::kTransitionProperty;
//    case CSSPropertyID::kTransitionTimingFunction:
//      return mojom::blink::CSSSampleId::kTransitionTimingFunction;
//    case CSSPropertyID::kTranslate:
//      return mojom::blink::CSSSampleId::kTranslate;
//    case CSSPropertyID::kTypes:
//      return mojom::blink::CSSSampleId::kTypes;
//    case CSSPropertyID::kUnicodeBidi:
//      return mojom::blink::CSSSampleId::kUnicodeBidi;
//    case CSSPropertyID::kUnicodeRange:
//      return mojom::blink::CSSSampleId::kUnicodeRange;
//    case CSSPropertyID::kUserSelect:
//      return mojom::blink::CSSSampleId::kUserSelect;
//    case CSSPropertyID::kVectorEffect:
//      return mojom::blink::CSSSampleId::kVectorEffect;
//    case CSSPropertyID::kVerticalAlign:
//      return mojom::blink::CSSSampleId::kVerticalAlign;
//    case CSSPropertyID::kViewTimelineAxis:
//      return mojom::blink::CSSSampleId::kViewTimelineAxis;
//    case CSSPropertyID::kViewTimelineInset:
//      return mojom::blink::CSSSampleId::kViewTimelineInset;
//    case CSSPropertyID::kViewTimelineName:
//      return mojom::blink::CSSSampleId::kViewTimelineName;
//    case CSSPropertyID::kViewTransitionClass:
//      return mojom::blink::CSSSampleId::kViewTransitionClass;
//    case CSSPropertyID::kViewTransitionName:
//      return mojom::blink::CSSSampleId::kViewTransitionName;
//    case CSSPropertyID::kVisibility:
//      return mojom::blink::CSSSampleId::kVisibility;
//    case CSSPropertyID::kWebkitBorderHorizontalSpacing:
//      return mojom::blink::CSSSampleId::kWebkitBorderHorizontalSpacing;
//    case CSSPropertyID::kWebkitBorderImage:
//      return mojom::blink::CSSSampleId::kWebkitBorderImage;
//    case CSSPropertyID::kWebkitBorderVerticalSpacing:
//      return mojom::blink::CSSSampleId::kWebkitBorderVerticalSpacing;
//    case CSSPropertyID::kWebkitBoxAlign:
//      return mojom::blink::CSSSampleId::kWebkitBoxAlign;
//    case CSSPropertyID::kWebkitBoxDecorationBreak:
//      return mojom::blink::CSSSampleId::kWebkitBoxDecorationBreak;
//    case CSSPropertyID::kWebkitBoxDirection:
//      return mojom::blink::CSSSampleId::kWebkitBoxDirection;
//    case CSSPropertyID::kWebkitBoxFlex:
//      return mojom::blink::CSSSampleId::kWebkitBoxFlex;
//    case CSSPropertyID::kWebkitBoxOrdinalGroup:
//      return mojom::blink::CSSSampleId::kWebkitBoxOrdinalGroup;
//    case CSSPropertyID::kWebkitBoxOrient:
//      return mojom::blink::CSSSampleId::kWebkitBoxOrient;
//    case CSSPropertyID::kWebkitBoxPack:
//      return mojom::blink::CSSSampleId::kWebkitBoxPack;
//    case CSSPropertyID::kWebkitBoxReflect:
//      return mojom::blink::CSSSampleId::kWebkitBoxReflect;
//    case CSSPropertyID::kWebkitLineBreak:
//      return mojom::blink::CSSSampleId::kWebkitLineBreak;
//    case CSSPropertyID::kWebkitLineClamp:
//      return mojom::blink::CSSSampleId::kWebkitLineClamp;
//    case CSSPropertyID::kWebkitMaskBoxImageOutset:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImageOutset;
//    case CSSPropertyID::kWebkitMaskBoxImageRepeat:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImageRepeat;
//    case CSSPropertyID::kWebkitMaskBoxImageSlice:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImageSlice;
//    case CSSPropertyID::kWebkitMaskBoxImageSource:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImageSource;
//    case CSSPropertyID::kWebkitMaskBoxImageWidth:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImageWidth;
//    case CSSPropertyID::kWebkitMaskPositionX:
//      return mojom::blink::CSSSampleId::kWebkitMaskPositionX;
//    case CSSPropertyID::kWebkitMaskPositionY:
//      return mojom::blink::CSSSampleId::kWebkitMaskPositionY;
//    case CSSPropertyID::kWebkitPerspectiveOriginX:
//      return mojom::blink::CSSSampleId::kWebkitPerspectiveOriginX;
//    case CSSPropertyID::kWebkitPerspectiveOriginY:
//      return mojom::blink::CSSSampleId::kWebkitPerspectiveOriginY;
//    case CSSPropertyID::kWebkitPrintColorAdjust:
//      return mojom::blink::CSSSampleId::kWebkitPrintColorAdjust;
//    case CSSPropertyID::kWebkitRtlOrdering:
//      return mojom::blink::CSSSampleId::kWebkitRtlOrdering;
//    case CSSPropertyID::kWebkitRubyPosition:
//      return mojom::blink::CSSSampleId::kWebkitRubyPosition;
//    case CSSPropertyID::kWebkitTapHighlightColor:
//      return mojom::blink::CSSSampleId::kWebkitTapHighlightColor;
//    case CSSPropertyID::kWebkitTextCombine:
//      return mojom::blink::CSSSampleId::kWebkitTextCombine;
//    case CSSPropertyID::kWebkitTextDecorationsInEffect:
//      return mojom::blink::CSSSampleId::kWebkitTextDecorationsInEffect;
//    case CSSPropertyID::kWebkitTextFillColor:
//      return mojom::blink::CSSSampleId::kWebkitTextFillColor;
//    case CSSPropertyID::kWebkitTextSecurity:
//      return mojom::blink::CSSSampleId::kWebkitTextSecurity;
//    case CSSPropertyID::kWebkitTextStrokeColor:
//      return mojom::blink::CSSSampleId::kWebkitTextStrokeColor;
//    case CSSPropertyID::kWebkitTextStrokeWidth:
//      return mojom::blink::CSSSampleId::kWebkitTextStrokeWidth;
//    case CSSPropertyID::kWebkitTransformOriginX:
//      return mojom::blink::CSSSampleId::kWebkitTransformOriginX;
//    case CSSPropertyID::kWebkitTransformOriginY:
//      return mojom::blink::CSSSampleId::kWebkitTransformOriginY;
//    case CSSPropertyID::kWebkitTransformOriginZ:
//      return mojom::blink::CSSSampleId::kWebkitTransformOriginZ;
//    case CSSPropertyID::kWebkitUserDrag:
//      return mojom::blink::CSSSampleId::kWebkitUserDrag;
//    case CSSPropertyID::kWebkitUserModify:
//      return mojom::blink::CSSSampleId::kWebkitUserModify;
//    case CSSPropertyID::kWhiteSpaceCollapse:
//      return mojom::blink::CSSSampleId::kWhiteSpaceCollapse;
//    case CSSPropertyID::kWidows:
//      return mojom::blink::CSSSampleId::kWidows;
//    case CSSPropertyID::kWidth:
//      return mojom::blink::CSSSampleId::kWidth;
//    case CSSPropertyID::kWillChange:
//      return mojom::blink::CSSSampleId::kWillChange;
//    case CSSPropertyID::kWordBreak:
//      return mojom::blink::CSSSampleId::kWordBreak;
//    case CSSPropertyID::kWordSpacing:
//      return mojom::blink::CSSSampleId::kWordSpacing;
//    case CSSPropertyID::kX:
//      return mojom::blink::CSSSampleId::kX;
//    case CSSPropertyID::kY:
//      return mojom::blink::CSSSampleId::kY;
//    case CSSPropertyID::kZIndex:
//      return mojom::blink::CSSSampleId::kZIndex;
//    case CSSPropertyID::kAlternativeAnimationWithTimeline:
//      return mojom::blink::CSSSampleId::kAnimation;
//    case CSSPropertyID::kAnimation:
//      return mojom::blink::CSSSampleId::kAnimation;
//    case CSSPropertyID::kAnimationRange:
//      return mojom::blink::CSSSampleId::kAnimationRange;
//    case CSSPropertyID::kBackground:
//      return mojom::blink::CSSSampleId::kBackground;
//    case CSSPropertyID::kBackgroundPosition:
//      return mojom::blink::CSSSampleId::kBackgroundPosition;
//    case CSSPropertyID::kBorder:
//      return mojom::blink::CSSSampleId::kBorder;
//    case CSSPropertyID::kBorderBlock:
//      return mojom::blink::CSSSampleId::kBorderBlock;
//    case CSSPropertyID::kBorderBlockColor:
//      return mojom::blink::CSSSampleId::kBorderBlockColor;
//    case CSSPropertyID::kBorderBlockEnd:
//      return mojom::blink::CSSSampleId::kBorderBlockEnd;
//    case CSSPropertyID::kBorderBlockStart:
//      return mojom::blink::CSSSampleId::kBorderBlockStart;
//    case CSSPropertyID::kBorderBlockStyle:
//      return mojom::blink::CSSSampleId::kBorderBlockStyle;
//    case CSSPropertyID::kBorderBlockWidth:
//      return mojom::blink::CSSSampleId::kBorderBlockWidth;
//    case CSSPropertyID::kBorderBottom:
//      return mojom::blink::CSSSampleId::kBorderBottom;
//    case CSSPropertyID::kBorderColor:
//      return mojom::blink::CSSSampleId::kBorderColor;
//    case CSSPropertyID::kBorderImage:
//      return mojom::blink::CSSSampleId::kBorderImage;
//    case CSSPropertyID::kBorderInline:
//      return mojom::blink::CSSSampleId::kBorderInline;
//    case CSSPropertyID::kBorderInlineColor:
//      return mojom::blink::CSSSampleId::kBorderInlineColor;
//    case CSSPropertyID::kBorderInlineEnd:
//      return mojom::blink::CSSSampleId::kBorderInlineEnd;
//    case CSSPropertyID::kBorderInlineStart:
//      return mojom::blink::CSSSampleId::kBorderInlineStart;
//    case CSSPropertyID::kBorderInlineStyle:
//      return mojom::blink::CSSSampleId::kBorderInlineStyle;
//    case CSSPropertyID::kBorderInlineWidth:
//      return mojom::blink::CSSSampleId::kBorderInlineWidth;
//    case CSSPropertyID::kBorderLeft:
//      return mojom::blink::CSSSampleId::kBorderLeft;
//    case CSSPropertyID::kBorderRadius:
//      return mojom::blink::CSSSampleId::kBorderRadius;
//    case CSSPropertyID::kBorderRight:
//      return mojom::blink::CSSSampleId::kBorderRight;
//    case CSSPropertyID::kBorderSpacing:
//      return mojom::blink::CSSSampleId::kBorderSpacing;
//    case CSSPropertyID::kBorderStyle:
//      return mojom::blink::CSSSampleId::kBorderStyle;
//    case CSSPropertyID::kBorderTop:
//      return mojom::blink::CSSSampleId::kBorderTop;
//    case CSSPropertyID::kBorderWidth:
//      return mojom::blink::CSSSampleId::kBorderWidth;
//    case CSSPropertyID::kColumnRule:
//      return mojom::blink::CSSSampleId::kColumnRule;
//    case CSSPropertyID::kColumns:
//      return mojom::blink::CSSSampleId::kColumns;
//    case CSSPropertyID::kContainIntrinsicSize:
//      return mojom::blink::CSSSampleId::kContainIntrinsicSize;
//    case CSSPropertyID::kContainer:
//      return mojom::blink::CSSSampleId::kContainer;
//    case CSSPropertyID::kFlex:
//      return mojom::blink::CSSSampleId::kFlex;
//    case CSSPropertyID::kFlexFlow:
//      return mojom::blink::CSSSampleId::kFlexFlow;
//    case CSSPropertyID::kFont:
//      return mojom::blink::CSSSampleId::kFont;
//    case CSSPropertyID::kFontSynthesis:
//      return mojom::blink::CSSSampleId::kFontSynthesis;
//    case CSSPropertyID::kFontVariant:
//      return mojom::blink::CSSSampleId::kFontVariant;
//    case CSSPropertyID::kGap:
//      return mojom::blink::CSSSampleId::kGap;
//    case CSSPropertyID::kGrid:
//      return mojom::blink::CSSSampleId::kGrid;
//    case CSSPropertyID::kGridArea:
//      return mojom::blink::CSSSampleId::kGridArea;
//    case CSSPropertyID::kGridColumn:
//      return mojom::blink::CSSSampleId::kGridColumn;
//    case CSSPropertyID::kGridRow:
//      return mojom::blink::CSSSampleId::kGridRow;
//    case CSSPropertyID::kGridTemplate:
//      return mojom::blink::CSSSampleId::kGridTemplate;
//    case CSSPropertyID::kInset:
//      return mojom::blink::CSSSampleId::kInset;
//    case CSSPropertyID::kInsetBlock:
//      return mojom::blink::CSSSampleId::kInsetBlock;
//    case CSSPropertyID::kInsetInline:
//      return mojom::blink::CSSSampleId::kInsetInline;
//    case CSSPropertyID::kListStyle:
//      return mojom::blink::CSSSampleId::kListStyle;
//    case CSSPropertyID::kMargin:
//      return mojom::blink::CSSSampleId::kMargin;
//    case CSSPropertyID::kMarginBlock:
//      return mojom::blink::CSSSampleId::kMarginBlock;
//    case CSSPropertyID::kMarginInline:
//      return mojom::blink::CSSSampleId::kMarginInline;
//    case CSSPropertyID::kMarker:
//      return mojom::blink::CSSSampleId::kMarker;
//    case CSSPropertyID::kMask:
//      return mojom::blink::CSSSampleId::kMask;
//    case CSSPropertyID::kMaskPosition:
//      return mojom::blink::CSSSampleId::kMaskPosition;
//    case CSSPropertyID::kOffset:
//      return mojom::blink::CSSSampleId::kOffset;
//    case CSSPropertyID::kOutline:
//      return mojom::blink::CSSSampleId::kOutline;
//    case CSSPropertyID::kOverflow:
//      return mojom::blink::CSSSampleId::kOverflow;
//    case CSSPropertyID::kOverscrollBehavior:
//      return mojom::blink::CSSSampleId::kOverscrollBehavior;
//    case CSSPropertyID::kPadding:
//      return mojom::blink::CSSSampleId::kPadding;
//    case CSSPropertyID::kPaddingBlock:
//      return mojom::blink::CSSSampleId::kPaddingBlock;
//    case CSSPropertyID::kPaddingInline:
//      return mojom::blink::CSSSampleId::kPaddingInline;
//    case CSSPropertyID::kPageBreakAfter:
//      return mojom::blink::CSSSampleId::kPageBreakAfter;
//    case CSSPropertyID::kPageBreakBefore:
//      return mojom::blink::CSSSampleId::kPageBreakBefore;
//    case CSSPropertyID::kPageBreakInside:
//      return mojom::blink::CSSSampleId::kPageBreakInside;
//    case CSSPropertyID::kPlaceContent:
//      return mojom::blink::CSSSampleId::kPlaceContent;
//    case CSSPropertyID::kPlaceItems:
//      return mojom::blink::CSSSampleId::kPlaceItems;
//    case CSSPropertyID::kPlaceSelf:
//      return mojom::blink::CSSSampleId::kPlaceSelf;
//    case CSSPropertyID::kPositionTry:
//      return mojom::blink::CSSSampleId::kPositionTry;
//    case CSSPropertyID::kScrollMargin:
//      return mojom::blink::CSSSampleId::kScrollMargin;
//    case CSSPropertyID::kScrollMarginBlock:
//      return mojom::blink::CSSSampleId::kScrollMarginBlock;
//    case CSSPropertyID::kScrollMarginInline:
//      return mojom::blink::CSSSampleId::kScrollMarginInline;
//    case CSSPropertyID::kScrollPadding:
//      return mojom::blink::CSSSampleId::kScrollPadding;
//    case CSSPropertyID::kScrollPaddingBlock:
//      return mojom::blink::CSSSampleId::kScrollPaddingBlock;
//    case CSSPropertyID::kScrollPaddingInline:
//      return mojom::blink::CSSSampleId::kScrollPaddingInline;
//    case CSSPropertyID::kScrollStart:
//      return mojom::blink::CSSSampleId::kScrollStart;
//    case CSSPropertyID::kScrollStartTarget:
//      return mojom::blink::CSSSampleId::kScrollStartTarget;
//    case CSSPropertyID::kScrollTimeline:
//      return mojom::blink::CSSSampleId::kScrollTimeline;
//    case CSSPropertyID::kTextDecoration:
//      return mojom::blink::CSSSampleId::kTextDecoration;
//    case CSSPropertyID::kTextEmphasis:
//      return mojom::blink::CSSSampleId::kTextEmphasis;
//    case CSSPropertyID::kTextSpacing:
//      return mojom::blink::CSSSampleId::kTextSpacing;
//    case CSSPropertyID::kTransition:
//      return mojom::blink::CSSSampleId::kTransition;
//    case CSSPropertyID::kViewTimeline:
//      return mojom::blink::CSSSampleId::kViewTimeline;
//    case CSSPropertyID::kWebkitColumnBreakAfter:
//      return mojom::blink::CSSSampleId::kWebkitColumnBreakAfter;
//    case CSSPropertyID::kWebkitColumnBreakBefore:
//      return mojom::blink::CSSSampleId::kWebkitColumnBreakBefore;
//    case CSSPropertyID::kWebkitColumnBreakInside:
//      return mojom::blink::CSSSampleId::kWebkitColumnBreakInside;
//    case CSSPropertyID::kWebkitMaskBoxImage:
//      return mojom::blink::CSSSampleId::kWebkitMaskBoxImage;
//    case CSSPropertyID::kWebkitTextStroke:
//      return mojom::blink::CSSSampleId::kWebkitTextStroke;
//    case CSSPropertyID::kWhiteSpace:
//      return mojom::blink::CSSSampleId::kWhiteSpace;
//    case CSSPropertyID::kAliasWebkitAppearance:
//      return mojom::blink::CSSSampleId::kAliasWebkitAppearance;
//    case CSSPropertyID::kAliasWebkitAppRegion:
//      return mojom::blink::CSSSampleId::kAliasWebkitAppRegion;
//    case CSSPropertyID::kAliasWebkitMaskClip:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskClip;
//    case CSSPropertyID::kAliasWebkitMaskComposite:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskComposite;
//    case CSSPropertyID::kAliasWebkitMaskImage:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskImage;
//    case CSSPropertyID::kAliasWebkitMaskOrigin:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskOrigin;
//    case CSSPropertyID::kAliasWebkitMaskRepeat:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskRepeat;
//    case CSSPropertyID::kAliasWebkitMaskSize:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskSize;
//    case CSSPropertyID::kAliasWebkitBorderEndColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderEndColor;
//    case CSSPropertyID::kAliasWebkitBorderEndStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderEndStyle;
//    case CSSPropertyID::kAliasWebkitBorderEndWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderEndWidth;
//    case CSSPropertyID::kAliasWebkitBorderStartColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderStartColor;
//    case CSSPropertyID::kAliasWebkitBorderStartStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderStartStyle;
//    case CSSPropertyID::kAliasWebkitBorderStartWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderStartWidth;
//    case CSSPropertyID::kAliasWebkitBorderBeforeColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBeforeColor;
//    case CSSPropertyID::kAliasWebkitBorderBeforeStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBeforeStyle;
//    case CSSPropertyID::kAliasWebkitBorderBeforeWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBeforeWidth;
//    case CSSPropertyID::kAliasWebkitBorderAfterColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderAfterColor;
//    case CSSPropertyID::kAliasWebkitBorderAfterStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderAfterStyle;
//    case CSSPropertyID::kAliasWebkitBorderAfterWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderAfterWidth;
//    case CSSPropertyID::kAliasWebkitMarginEnd:
//      return mojom::blink::CSSSampleId::kAliasWebkitMarginEnd;
//    case CSSPropertyID::kAliasWebkitMarginStart:
//      return mojom::blink::CSSSampleId::kAliasWebkitMarginStart;
//    case CSSPropertyID::kAliasWebkitMarginBefore:
//      return mojom::blink::CSSSampleId::kAliasWebkitMarginBefore;
//    case CSSPropertyID::kAliasWebkitMarginAfter:
//      return mojom::blink::CSSSampleId::kAliasWebkitMarginAfter;
//    case CSSPropertyID::kAliasWebkitPaddingEnd:
//      return mojom::blink::CSSSampleId::kAliasWebkitPaddingEnd;
//    case CSSPropertyID::kAliasWebkitPaddingStart:
//      return mojom::blink::CSSSampleId::kAliasWebkitPaddingStart;
//    case CSSPropertyID::kAliasWebkitPaddingBefore:
//      return mojom::blink::CSSSampleId::kAliasWebkitPaddingBefore;
//    case CSSPropertyID::kAliasWebkitPaddingAfter:
//      return mojom::blink::CSSSampleId::kAliasWebkitPaddingAfter;
//    case CSSPropertyID::kAliasWebkitLogicalWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitLogicalWidth;
//    case CSSPropertyID::kAliasWebkitLogicalHeight:
//      return mojom::blink::CSSSampleId::kAliasWebkitLogicalHeight;
//    case CSSPropertyID::kAliasWebkitMinLogicalWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitMinLogicalWidth;
//    case CSSPropertyID::kAliasWebkitMinLogicalHeight:
//      return mojom::blink::CSSSampleId::kAliasWebkitMinLogicalHeight;
//    case CSSPropertyID::kAliasWebkitMaxLogicalWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaxLogicalWidth;
//    case CSSPropertyID::kAliasWebkitMaxLogicalHeight:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaxLogicalHeight;
//    case CSSPropertyID::kAliasWebkitBorderAfter:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderAfter;
//    case CSSPropertyID::kAliasWebkitBorderBefore:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBefore;
//    case CSSPropertyID::kAliasWebkitBorderEnd:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderEnd;
//    case CSSPropertyID::kAliasWebkitBorderStart:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderStart;
//    case CSSPropertyID::kAliasWebkitMask:
//      return mojom::blink::CSSSampleId::kAliasWebkitMask;
//    case CSSPropertyID::kAliasWebkitMaskPosition:
//      return mojom::blink::CSSSampleId::kAliasWebkitMaskPosition;
//    case CSSPropertyID::kAliasEpubCaptionSide:
//      return mojom::blink::CSSSampleId::kAliasEpubCaptionSide;
//    case CSSPropertyID::kAliasEpubTextCombine:
//      return mojom::blink::CSSSampleId::kAliasEpubTextCombine;
//    case CSSPropertyID::kAliasEpubTextEmphasis:
//      return mojom::blink::CSSSampleId::kAliasEpubTextEmphasis;
//    case CSSPropertyID::kAliasEpubTextEmphasisColor:
//      return mojom::blink::CSSSampleId::kAliasEpubTextEmphasisColor;
//    case CSSPropertyID::kAliasEpubTextEmphasisStyle:
//      return mojom::blink::CSSSampleId::kAliasEpubTextEmphasisStyle;
//    case CSSPropertyID::kAliasEpubTextOrientation:
//      return mojom::blink::CSSSampleId::kAliasEpubTextOrientation;
//    case CSSPropertyID::kAliasEpubTextTransform:
//      return mojom::blink::CSSSampleId::kAliasEpubTextTransform;
//    case CSSPropertyID::kAliasEpubWordBreak:
//      return mojom::blink::CSSSampleId::kAliasEpubWordBreak;
//    case CSSPropertyID::kAliasEpubWritingMode:
//      return mojom::blink::CSSSampleId::kAliasEpubWritingMode;
//    case CSSPropertyID::kAliasWebkitAlignContent:
//      return mojom::blink::CSSSampleId::kAliasWebkitAlignContent;
//    case CSSPropertyID::kAliasWebkitAlignItems:
//      return mojom::blink::CSSSampleId::kAliasWebkitAlignItems;
//    case CSSPropertyID::kAliasWebkitAlignSelf:
//      return mojom::blink::CSSSampleId::kAliasWebkitAlignSelf;
//    case CSSPropertyID::kAliasWebkitAnimation:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimation;
//    case CSSPropertyID::kAliasWebkitAlternativeAnimationWithTimeline:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimation;
//    case CSSPropertyID::kAliasWebkitAnimationDelay:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationDelay;
//    case CSSPropertyID::kAliasWebkitAnimationDirection:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationDirection;
//    case CSSPropertyID::kAliasWebkitAnimationDuration:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationDuration;
//    case CSSPropertyID::kAliasWebkitAnimationFillMode:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationFillMode;
//    case CSSPropertyID::kAliasWebkitAnimationIterationCount:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationIterationCount;
//    case CSSPropertyID::kAliasWebkitAnimationName:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationName;
//    case CSSPropertyID::kAliasWebkitAnimationPlayState:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationPlayState;
//    case CSSPropertyID::kAliasWebkitAnimationTimingFunction:
//      return mojom::blink::CSSSampleId::kAliasWebkitAnimationTimingFunction;
//    case CSSPropertyID::kAliasWebkitBackfaceVisibility:
//      return mojom::blink::CSSSampleId::kAliasWebkitBackfaceVisibility;
//    case CSSPropertyID::kAliasWebkitBackgroundClip:
//      return mojom::blink::CSSSampleId::kAliasWebkitBackgroundClip;
//    case CSSPropertyID::kAliasWebkitBackgroundOrigin:
//      return mojom::blink::CSSSampleId::kAliasWebkitBackgroundOrigin;
//    case CSSPropertyID::kAliasWebkitBackgroundSize:
//      return mojom::blink::CSSSampleId::kAliasWebkitBackgroundSize;
//    case CSSPropertyID::kAliasWebkitBorderBottomLeftRadius:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBottomLeftRadius;
//    case CSSPropertyID::kAliasWebkitBorderBottomRightRadius:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderBottomRightRadius;
//    case CSSPropertyID::kAliasWebkitBorderRadius:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderRadius;
//    case CSSPropertyID::kAliasWebkitBorderTopLeftRadius:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderTopLeftRadius;
//    case CSSPropertyID::kAliasWebkitBorderTopRightRadius:
//      return mojom::blink::CSSSampleId::kAliasWebkitBorderTopRightRadius;
//    case CSSPropertyID::kAliasWebkitBoxShadow:
//      return mojom::blink::CSSSampleId::kAliasWebkitBoxShadow;
//    case CSSPropertyID::kAliasWebkitBoxSizing:
//      return mojom::blink::CSSSampleId::kAliasWebkitBoxSizing;
//    case CSSPropertyID::kAliasWebkitClipPath:
//      return mojom::blink::CSSSampleId::kAliasWebkitClipPath;
//    case CSSPropertyID::kAliasWebkitColumnCount:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnCount;
//    case CSSPropertyID::kAliasWebkitColumnGap:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnGap;
//    case CSSPropertyID::kAliasWebkitColumnRule:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnRule;
//    case CSSPropertyID::kAliasWebkitColumnRuleColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnRuleColor;
//    case CSSPropertyID::kAliasWebkitColumnRuleStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnRuleStyle;
//    case CSSPropertyID::kAliasWebkitColumnRuleWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnRuleWidth;
//    case CSSPropertyID::kAliasWebkitColumnSpan:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnSpan;
//    case CSSPropertyID::kAliasWebkitColumnWidth:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumnWidth;
//    case CSSPropertyID::kAliasWebkitColumns:
//      return mojom::blink::CSSSampleId::kAliasWebkitColumns;
//    case CSSPropertyID::kAliasWebkitFilter:
//      return mojom::blink::CSSSampleId::kAliasWebkitFilter;
//    case CSSPropertyID::kAliasWebkitFlex:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlex;
//    case CSSPropertyID::kAliasWebkitFlexBasis:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexBasis;
//    case CSSPropertyID::kAliasWebkitFlexDirection:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexDirection;
//    case CSSPropertyID::kAliasWebkitFlexFlow:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexFlow;
//    case CSSPropertyID::kAliasWebkitFlexGrow:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexGrow;
//    case CSSPropertyID::kAliasWebkitFlexShrink:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexShrink;
//    case CSSPropertyID::kAliasWebkitFlexWrap:
//      return mojom::blink::CSSSampleId::kAliasWebkitFlexWrap;
//    case CSSPropertyID::kAliasWebkitFontFeatureSettings:
//      return mojom::blink::CSSSampleId::kAliasWebkitFontFeatureSettings;
//    case CSSPropertyID::kAliasWebkitHyphenateCharacter:
//      return mojom::blink::CSSSampleId::kAliasWebkitHyphenateCharacter;
//    case CSSPropertyID::kAliasWebkitJustifyContent:
//      return mojom::blink::CSSSampleId::kAliasWebkitJustifyContent;
//    case CSSPropertyID::kAliasWebkitOpacity:
//      return mojom::blink::CSSSampleId::kAliasWebkitOpacity;
//    case CSSPropertyID::kAliasWebkitOrder:
//      return mojom::blink::CSSSampleId::kAliasWebkitOrder;
//    case CSSPropertyID::kAliasWebkitPerspective:
//      return mojom::blink::CSSSampleId::kAliasWebkitPerspective;
//    case CSSPropertyID::kAliasWebkitPerspectiveOrigin:
//      return mojom::blink::CSSSampleId::kAliasWebkitPerspectiveOrigin;
//    case CSSPropertyID::kAliasWebkitShapeImageThreshold:
//      return mojom::blink::CSSSampleId::kAliasWebkitShapeImageThreshold;
//    case CSSPropertyID::kAliasWebkitShapeMargin:
//      return mojom::blink::CSSSampleId::kAliasWebkitShapeMargin;
//    case CSSPropertyID::kAliasWebkitShapeOutside:
//      return mojom::blink::CSSSampleId::kAliasWebkitShapeOutside;
//    case CSSPropertyID::kAliasWebkitTextEmphasis:
//      return mojom::blink::CSSSampleId::kAliasWebkitTextEmphasis;
//    case CSSPropertyID::kAliasWebkitTextEmphasisColor:
//      return mojom::blink::CSSSampleId::kAliasWebkitTextEmphasisColor;
//    case CSSPropertyID::kAliasWebkitTextEmphasisPosition:
//      return mojom::blink::CSSSampleId::kAliasWebkitTextEmphasisPosition;
//    case CSSPropertyID::kAliasWebkitTextEmphasisStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitTextEmphasisStyle;
//    case CSSPropertyID::kAliasWebkitTextSizeAdjust:
//      return mojom::blink::CSSSampleId::kAliasWebkitTextSizeAdjust;
//    case CSSPropertyID::kAliasWebkitTransform:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransform;
//    case CSSPropertyID::kAliasWebkitTransformOrigin:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransformOrigin;
//    case CSSPropertyID::kAliasWebkitTransformStyle:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransformStyle;
//    case CSSPropertyID::kAliasWebkitTransition:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransition;
//    case CSSPropertyID::kAliasWebkitTransitionDelay:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransitionDelay;
//    case CSSPropertyID::kAliasWebkitTransitionDuration:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransitionDuration;
//    case CSSPropertyID::kAliasWebkitTransitionProperty:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransitionProperty;
//    case CSSPropertyID::kAliasWebkitTransitionTimingFunction:
//      return mojom::blink::CSSSampleId::kAliasWebkitTransitionTimingFunction;
//    case CSSPropertyID::kAliasWebkitUserSelect:
//      return mojom::blink::CSSSampleId::kAliasWebkitUserSelect;
//    case CSSPropertyID::kAliasWordWrap:
//      return mojom::blink::CSSSampleId::kAliasWordWrap;
//    case CSSPropertyID::kAliasGridColumnGap:
//      return mojom::blink::CSSSampleId::kAliasGridColumnGap;
//    case CSSPropertyID::kAliasGridRowGap:
//      return mojom::blink::CSSSampleId::kAliasGridRowGap;
//    case CSSPropertyID::kAliasGridGap:
//      return mojom::blink::CSSSampleId::kAliasGridGap;
//
//    case CSSPropertyID::kVariable:
//      return mojom::blink::CSSSampleId::kVariable;
//
//    case CSSPropertyID::kInvalid:
//      return mojom::blink::CSSSampleId::kInvalid;

    // IMPORTANT: Do not add a default case to ensure a compile error if we have
    // a mismatch.
//  }
//
//  NOTREACHED();
//}

int ResolveCSSPropertyAlias(int value) {
  static constexpr uint16_t kLookupTable[] = {
    8,
    63,
    257,
    258,
    4,
    260,
    261,
    262,
    101,
    102,
    103,
    104,
    105,
    106,
    85,
    86,
    87,
    82,
    83,
    84,
    249,
    250,
    247,
    246,
    310,
    311,
    308,
    307,
    199,
    81,
    272,
    270,
    268,
    266,
    499,
    500,
    508,
    509,
    542,
    543,
    127,
    471,
    568,
    409,
    411,
    39,
    416,
    486,
    40,
    45,
    46,
    47,
    492,
    491,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    62,
    67,
    70,
    73,
    77,
    89,
    90,
    513,
    116,
    117,
    121,
    122,
    131,
    136,
    138,
    519,
    139,
    140,
    141,
    142,
    143,
    520,
    170,
    523,
    171,
    172,
    524,
    173,
    174,
    175,
    12,
    191,
    233,
    285,
    286,
    318,
    319,
    372,
    373,
    374,
    568,
    409,
    410,
    411,
    415,
    423,
    425,
    426,
    570,
    428,
    429,
    430,
    431,
    436,
    297,
    138,
    334,
    528,
  };
  return kLookupTable[value - 578];
}

}  // namespace blink
